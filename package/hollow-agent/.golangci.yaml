version: "2"
linters:
  default: none
  enable:
    - copyloopvar
    - depguard
    - errorlint
    - govet
    - ineffassign
    - intrange
    - perfsprint
    - staticcheck
    - unused
    - mirror
    - durationcheck
    - errorlint
    - govet
    - unconvert
    - usetesting
    - zerologlint
    - testifylint
  settings:
    depguard:
      rules:
        main:
          deny:
            - pkg: encoding/json
              desc: use github.com/segmentio/encoding/json
            - pkg: github.com/sirupsen/logrus
              desc: logging is allowed only by github.com/rs/zerolog
            - pkg: golang.org/x/exp
              desc: exp is not allowed
            - pkg: github.com/portainer/libcrypto
              desc: use github.com/portainer/portainer/pkg/libcrypto
            - pkg: github.com/portainer/libhttp
              desc: use github.com/portainer/portainer/pkg/libhttp
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  enable:
    - gci
  settings:
    gci:
      sections:
        - Standard
        - Prefix(github.com/portainer)
        - Default
      custom-order: true
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
