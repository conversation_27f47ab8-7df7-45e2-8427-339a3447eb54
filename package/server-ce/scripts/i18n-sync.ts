#!/usr/bin/env node

/**
 * Portainer i18n Synchronization Script
 * Scans codebase for i18n patterns and updates English translation files.
 * Usage: yarn i18n:sync
 */

/* eslint-disable no-console */

import * as fs from 'fs';
import * as path from 'path';

interface I18nOptions {
  dryRun: boolean;
  verbose: boolean;
  backup: boolean;
  updateExisting: boolean;
  checkOnly: boolean;
}

interface I18nStats {
  filesScanned: number;
  keysFound: number;
  keysAdded: number;
  keysUpdated: number;
  conflictsFound: number;
}

interface I18nConflict {
  key: string;
  currentValue: string;
  newValue: string;
  filePath: string;
}

interface ExtractedKeyData {
  fallback: string;
  filePath: string;
}

class I18nSyncTool {
  private options: I18nOptions;
  private extractedKeys: Map<string, ExtractedKeyData>;
  private stats: I18nStats;
  private conflicts: I18nConflict[];

  constructor(options: Partial<I18nOptions> = {}) {
    this.options = {
      dryRun: false,
      verbose: false,
      backup: false,
      updateExisting: true, // New option to control updating existing keys
      checkOnly: false, // New option for commit hook warnings
      ...options,
    };
    this.extractedKeys = new Map(); // key -> fallback text
    this.stats = {
      filesScanned: 0,
      keysFound: 0,
      keysAdded: 0,
      keysUpdated: 0,
      conflictsFound: 0,
    };
    this.conflicts = []; // Track conflicts between source and translation files
  }

  /**
   * Main execution method
   */
  async run(): Promise<void> {
    const modeText = this.options.checkOnly ? ' (CHECK MODE)' : this.options.dryRun ? ' (DRY RUN)' : '';
    console.log(`🌍 Starting i18n synchronization...${modeText} 
`);

    // Scan source files for translation keys
    await this.scanSourceFiles();

    if (this.extractedKeys.size === 0) {
      console.log('No translation keys found in source files.');
      return;
    }

    console.log(`   Found ${this.extractedKeys.size} translation keys in ${this.stats.filesScanned} files
`);

    // Handle different modes
    if (this.options.checkOnly) {
      await this.checkForChanges();
    } else if (this.options.dryRun) {
      console.log('👀 Previewing changes (dry run mode)...');
      await this.previewChanges();
    } else {
      console.log('📝 Updating translation files...');
      await this.updateTranslationFiles();
    }

    // Print summary
    this.printSummary();
  }

  /**
   * Scan all source files for translation patterns
   */
  private async scanSourceFiles(): Promise<void> {
    console.log('📁 Scanning source files...');
    const sourceDir = path.join(process.cwd(), 'app');
    await this.scanDirectory(sourceDir);
  }

  /**
   * Recursively scan directory for source files
   */
  private async scanDirectory(dirPath: string): Promise<void> {
    try {
      const entries = fs.readdirSync(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);

        if (entry.isDirectory()) {
          // Skip node_modules and other irrelevant directories
          if (!['node_modules', '.git', 'dist', 'build'].includes(entry.name)) {
            await this.scanDirectory(fullPath);
          }
        } else if (entry.isFile()) {
          await this.scanFile(fullPath);
        }
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(`   - Error scanning directory ${dirPath}: ${error}`);
      }
    }
  }

  /**
   * Scan individual file for translation patterns
   */
  private async scanFile(filePath: string): Promise<void> {
    // Only scan relevant file types
    const ext = path.extname(filePath);
    if (!['.ts', '.tsx', '.js', '.jsx', '.html'].includes(ext)) {
      return;
    }

    this.stats.filesScanned++;
    const relativePath = path.relative(process.cwd(), filePath);

    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const matches = this.extractTranslationKeys(content);

      if (matches.length > 0) {
        if (this.options.verbose) {
          console.log(`   ✓ ${relativePath}`);
        }

        matches.forEach(({ key, fallback }) => {
          this.extractedKeys.set(key, { fallback, filePath: relativePath });
          this.stats.keysFound++;
        });
      } else if (this.options.verbose) {
        console.log(`   - ${relativePath} (no matches)`);
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(`   - Error reading ${relativePath}: ${error}`);
      }
    }
  }

  /**
   * Extract translation keys from file content
   */
  private extractTranslationKeys(content: string): Array<{ key: string; fallback: string }> {
    const matches: Array<{ key: string; fallback: string }> = [];

    // React pattern: t('key', 'fallback')
    const reactPattern = /\bt\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*['"`]([^'"`]*)['"`]\s*\)/g;
    let match: RegExpExecArray | null;

    while ((match = reactPattern.exec(content)) !== null) {
      const [, key, fallback] = match;
      if (key && fallback) {
        matches.push({ key: key.trim(), fallback: fallback.trim() });
      }
    }

    // AngularJS pattern: {{ 'key' | translate:'fallback' }}
    const angularPattern = /{{\s*['"`]([^'"`]+)['"`]\s*\|\s*translate\s*:\s*['"`]([^'"`]*)['"`]\s*}}/g;

    while ((match = angularPattern.exec(content)) !== null) {
      const [, key, fallback] = match;
      if (key && fallback) {
        matches.push({ key: key.trim(), fallback: fallback.trim() });
      }
    }

    // AngularJS attribute pattern: attribute="'key' | translate:'fallback'"
    const angularAttributePattern =
      /['"`]([^'"`]+)['"`]\s*\|\s*translate\s*:\s*['"`]([^'"`]*)['"`]/g;

    while ((match = angularAttributePattern.exec(content)) !== null) {
      const [, key, fallback] = match;
      if (key && fallback) {
        matches.push({ key: key.trim(), fallback: fallback.trim() });
      }
    }

    // AngularJS controller pattern: $translate.instant('key', 'fallback')
    const angularControllerPattern = /\$translate\.instant\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*['"`]([^'"`]*)['"`]\s*\)/g;
    while ((match = angularControllerPattern.exec(content)) !== null) {
      const [, key, fallback] = match;
      if (key && fallback) {
        matches.push({ key: key.trim(), fallback: fallback.trim() });
      }
    }

    return matches;
  }

  /**
   * Preview changes without making them
   */
  private async previewChanges(): Promise<void> {
    const translationFiles = this.getTranslationFiles();

    for (const filePath of translationFiles) {
      await this.previewTranslationFile(filePath);
    }
  }

  /**
   * Preview changes for a single translation file
   */
  private async previewTranslationFile(filePath: string): Promise<void> {
    const existingTranslations = this.loadTranslationFile(filePath);

    // Count new keys that would be added and existing keys that would be updated
    let wouldAddCount = 0;
    let wouldUpdateCount = 0;
    const newKeys: string[] = [];
    const updatedKeys: string[] = [];

    for (const [key, data] of this.extractedKeys) {
      const keyExists = this.hasNestedKey(existingTranslations, key);
      
      if (!keyExists) {
        wouldAddCount++;
        newKeys.push(`${key}: "${data.fallback}"`);
      } else if (this.options.updateExisting) {
        const currentValue = this.getNestedKey(existingTranslations, key);
        if (currentValue !== data.fallback) {
          wouldUpdateCount++;
          updatedKeys.push(`${key}: "${currentValue}" → "${data.fallback}"`);
        }
      }
    }

    // Report what would be changed
    const changes: string[] = [];
    if (wouldAddCount > 0) changes.push(`${wouldAddCount} new keys`);
    if (wouldUpdateCount > 0) changes.push(`${wouldUpdateCount} updated keys`);

    if (changes.length > 0) {
      console.log(`   📋 ${filePath} - Would add ${changes.join(', ')}:`);
      if (this.options.verbose) {
        if (newKeys.length > 0) {
          console.log('      New keys:');
          newKeys.slice(0, 3).forEach((key) => console.log(`        + ${key}`));
          if (newKeys.length > 3) {
            console.log(`        ... and ${newKeys.length - 3} more new keys`);
          }
        }
        if (updatedKeys.length > 0) {
          console.log('      Updated keys:');
          updatedKeys.slice(0, 3).forEach((key) => console.log(`        ~ ${key}`));
          if (updatedKeys.length > 3) {
            console.log(`        ... and ${updatedKeys.length - 3} more updated keys`);
          }
        }
      }
    } else {
      console.log(`   ✓ ${filePath} - No changes needed`);
    }
  }

  /**
   * Check for changes and warn developer (for commit hooks)
   */
  private async checkForChanges(): Promise<void> {
    const translationFiles = this.getTranslationFiles();
    let hasChanges = false;
    let totalNewKeys = 0;
    let totalUpdatedKeys = 0;

    for (const filePath of translationFiles) {
      const existingTranslations = this.loadTranslationFile(filePath);

      // Count new keys that would be added and existing keys that would be updated
      let newKeysCount = 0;
      let updatedKeysCount = 0;

      for (const [key, fallbackText] of this.extractedKeys) {
        const currentValue = this.getNestedKey(existingTranslations, key);

        if (currentValue === undefined) {
          newKeysCount++;
        } else if (this.options.updateExisting && currentValue !== fallbackText) {
          updatedKeysCount++;
        }
      }

      if (newKeysCount > 0 || updatedKeysCount > 0) {
        hasChanges = true;
        totalNewKeys += newKeysCount;
        totalUpdatedKeys += updatedKeysCount;
      }
    }

    if (hasChanges) {
      console.log('\n⚠️  TRANSLATION CHANGES DETECTED ⚠️');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log('🌍 Your changes introduce new or updated translation keys:');
      if (totalNewKeys > 0) {
        console.log(`   📝 ${totalNewKeys} new translation key(s) found`);
      }
      if (totalUpdatedKeys > 0) {
        console.log(`   🔄 ${totalUpdatedKeys} existing translation key(s) updated`);
      }
      console.log('\n💡 Please run the following command to update translation files:');
      console.log('   yarn i18n:sync');
      console.log('\n📚 This ensures all translation keys are properly synchronized.');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

      // Exit with non-zero code to indicate changes are needed
      process.exit(1);
    } else {
      console.log('✅ No translation changes detected - all good!');
    }
  }

  /**
   * Update all translation files
   */
  private async updateTranslationFiles(): Promise<void> {
    const translationFiles = this.getTranslationFiles();

    for (const filePath of translationFiles) {
      await this.updateTranslationFile(filePath);
    }
  }

  /**
   * Update a single translation file
   */
  private async updateTranslationFile(filePath: string): Promise<void> {
    const existingTranslations = this.loadTranslationFile(filePath);

    // Create backup if enabled
    if (this.options.backup) {
      const backupPath = `${filePath}.backup.${Date.now()}`;
      fs.writeFileSync(backupPath, JSON.stringify(existingTranslations, null, 2));
      console.log(`   📋 Created backup: ${backupPath}`);
    }

    // Merge with new keys and update existing ones
    const updatedTranslations = { ...existingTranslations };
    let addedCount = 0;
    let updatedCount = 0;

    for (const [key, data] of this.extractedKeys) {
      const keyExists = this.hasNestedKey(updatedTranslations, key);
      
      if (!keyExists) {
        // New key - add it
        this.setNestedKey(updatedTranslations, key, data.fallback);
        addedCount++;
      } else if (this.options.updateExisting) {
        // Existing key - check if fallback text has changed
        const currentValue = this.getNestedKey(updatedTranslations, key);
        if (currentValue !== data.fallback) {
          // Record the conflict for reporting
          this.conflicts.push({
            key,
            currentValue,
            newValue: data.fallback,
            filePath
          });
          
          // Update the translation with new fallback text
          this.setNestedKey(updatedTranslations, key, data.fallback);
          updatedCount++;
          this.stats.conflictsFound++;
          
          if (this.options.verbose) {
            console.log(`   🔄 Updated key '${key}': "${currentValue}" → "${data.fallback}"`);
          }
        }
      }
    }

    // Sort keys alphabetically for consistency
    const sortedTranslations = this.sortObjectKeys(updatedTranslations);

    // Write updated translations
    fs.writeFileSync(filePath, JSON.stringify(sortedTranslations, null, 2) + '\n');

    // Update statistics
    this.stats.keysAdded += addedCount;
    this.stats.keysUpdated += updatedCount;

    // Report results
    const changes: string[] = [];
    if (addedCount > 0) changes.push(`${addedCount} new keys`);
    if (updatedCount > 0) changes.push(`${updatedCount} updated keys`);

    if (changes.length > 0) {
      console.log(`   ✓ ${filePath} - Added ${changes.join(', ')}`);
    } else {
      console.log(`   ✓ ${filePath} - No changes needed`);
    }
  }

  /**
   * Get list of translation files to update
   */
  private getTranslationFiles(): string[] {
    const translationsDir = path.join(process.cwd(), 'translations', 'en');

    // Ensure translations directory exists
    if (!fs.existsSync(translationsDir)) {
      fs.mkdirSync(translationsDir, { recursive: true });
    }

    const translationFile = path.join(translationsDir, 'translation.json');
    return [translationFile];
  }

  /**
   * Load existing translation file
   */
  private loadTranslationFile(filePath: string): Record<string, any> {
    try {
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf-8');
        return JSON.parse(content);
      }
    } catch (error) {
      console.warn(`Warning: Could not parse ${filePath}, starting with empty translations`);
    }
    return {};
  }

  /**
   * Check if a nested key exists in the translations object
   */
  private hasNestedKey(obj: Record<string, any>, key: string): boolean {
    const parts = key.split('.');
    let current = obj;

    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return false;
      }
    }

    return true;
  }

  /**
   * Get the value of a nested key from the translations object
   */
  private getNestedKey(obj: Record<string, any>, key: string): any {
    const parts = key.split('.');
    let current = obj;

    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return undefined;
      }
    }

    return current;
  }

  /**
   * Set a nested key in the translations object
   */
  private setNestedKey(obj: Record<string, any>, key: string, value: string): void {
    const parts = key.split('.');
    let current = obj;

    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!(part in current) || typeof current[part] !== 'object') {
        current[part] = {};
      }
      current = current[part];
    }

    current[parts[parts.length - 1]] = value;
  }

  /**
   * Recursively sort object keys alphabetically
   */
  private sortObjectKeys(obj: any): any {
    if (obj === null || typeof obj !== 'object' || Array.isArray(obj)) {
      return obj;
    }

    const sortedObj: Record<string, any> = {};
    const keys = Object.keys(obj).sort();

    for (const key of keys) {
      sortedObj[key] = this.sortObjectKeys(obj[key]);
    }

    return sortedObj;
  }

  /**
   * Print summary of the sync operation
   */
  private printSummary(): void {
    console.log('📊 Synchronization Summary:');
    console.log(`   Files scanned: ${this.stats.filesScanned}`);
    console.log(`   Translation keys found: ${this.stats.keysFound}`);
    console.log(`   New keys added: ${this.stats.keysAdded}`);
    console.log(`   Existing keys updated: ${this.stats.keysUpdated}`);

    // Report conflicts if any were found
    if (this.stats.conflictsFound > 0) {
      console.log(`   Fallback text conflicts resolved: ${this.stats.conflictsFound}`);

      if (this.options.verbose && this.conflicts.length > 0) {
        console.log('\n🔄 Fallback Text Updates:');
        this.conflicts.forEach(conflict => {
          console.log(`   • ${conflict.key}:`);
          console.log(`     Old: "${conflict.currentValue}"`);
          console.log(`     New: "${conflict.newValue}"`);
        });
      } else if (this.conflicts.length > 0) {
        console.log('\n💡 Use --verbose to see detailed fallback text changes');
      }
    }

    if (this.stats.keysFound === 0) {
      console.log('\n💡 No translation keys found. Start using i18n in your code:');
      console.log("   React: t('my.key', 'Default text')");
      console.log("   AngularJS: {{ 'my.key' | translate:'Default text' }}");
    } else {
      console.log('\n✅ i18n synchronization completed successfully!');
    }
  }
}

/**
 * Show help message
 */
function showHelp(): void {
  console.log(`
Portainer i18n Synchronization Tool

Scans the codebase for translation patterns and updates English translation files.
Only updates English translations - other languages are managed by the AI translation API.

Usage:
  yarn i18n:sync [options]

Options:
  --help, -h         Show this help message
  --dry-run          Show what would be done without making changes
  --check-only       Check for changes and warn (for git hooks) - exits with code 1 if changes found
  --verbose, -v      Show detailed output including files without matches
  --backup           Create backup files before making changes (default: false)
  --no-backup        Skip creating backup files
  --update-existing  Update existing keys when fallback text changes (default: true)
  --no-update        Skip updating existing keys, only add new ones

Examples:
  yarn i18n:sync                    # Sync translations with default options
  yarn i18n:sync --dry-run          # Preview changes without making them
  yarn i18n:sync --verbose          # Show detailed output
  yarn i18n:sync --no-backup        # Skip creating backup files
  yarn i18n:sync --no-update        # Only add new keys, don't update existing ones

Supported patterns:
  React:     t('key', 'fallback text')
  AngularJS: {{ 'key' | translate:'fallback text' }}
`);
}

/**
 * Parse command line arguments
 */
function parseArguments(): I18nOptions {
  const args = process.argv.slice(2);
  const options: I18nOptions = {
    help: false,
    dryRun: false,
    verbose: false,
    backup: false,
    updateExisting: true,
    checkOnly: false,
  } as I18nOptions & { help: boolean };

  for (const arg of args) {
    switch (arg) {
      case '--help':
      case '-h':
        (options as any).help = true;
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--check-only':
        options.checkOnly = true;
        options.dryRun = true; // check-only implies dry-run
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--backup':
        options.backup = true;
        break;
      case '--no-backup':
        options.backup = false;
        break;
      case '--update-existing':
        options.updateExisting = true;
        break;
      case '--no-update':
        options.updateExisting = false;
        break;
      default:
        console.error(`Unknown option: ${arg}`);
        console.error('Use --help for usage information');
        process.exit(1);
    }
  }

  return options;
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  try {
    const options = parseArguments();

    if ((options as any).help) {
      showHelp();
      return;
    }

    const tool = new I18nSyncTool(options);
    await tool.run();
  } catch (error) {
    console.error('Error during i18n synchronization:', error);
    process.exit(1);
  }
}

// Execute if this file is run directly
if (require.main === module) {
  main().catch(console.error);
}
