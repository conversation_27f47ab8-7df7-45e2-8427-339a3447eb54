angular.module('portainer.app').service('I18nService', I18nService);

function I18nService($rootScope) {
  'ngInject';

  const service = {
    currentLanguage: 'en',
    changeLanguage: changeLanguage,
    getCurrentLanguage: getCurrentLanguage,
    init: init,
  };

  return service;

  function init() {
    window.addEventListener('languageChanged', function (event) {
      const newLanguage = event.detail.language;
      service.currentLanguage = newLanguage;

      $rootScope.$broadcast('languageChanged', newLanguage);

      if (!$rootScope.$$phase) {
        $rootScope.$apply();
      }
    });

    const storedLanguage = localStorage.getItem('portainer-language');
    if (storedLanguage) {
      service.currentLanguage = storedLanguage;
    }
  }

  function changeLanguage(language) {
    service.currentLanguage = language;
    localStorage.setItem('portainer-language', language);

    const event = new CustomEvent('languageChanged', {
      detail: { language },
    });
    window.dispatchEvent(event);

    $rootScope.$broadcast('languageChanged', language);
  }

  function getCurrentLanguage() {
    return service.currentLanguage;
  }
}

// Create a $translate service that bridges to i18next
angular.module('portainer.app').service('$translate', $translateService);

function $translateService() {
  'ngInject';

  const service = {
    instant: instant,
  };

  return service;

  function instant(key, fallback) {
    if (typeof window !== 'undefined' && window.i18n && window.i18n.t) {
      const translation = window.i18n.t(key);
      if (translation !== key) {
        return translation;
      }
    }

    return fallback || key;
  }
}
