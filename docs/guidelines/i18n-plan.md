# Portainer i18n Integration Master Plan

## Overview

This document outlines the comprehensive plan for migrating hardcoded strings to internationalization keys in both React and Angular codebases of Portainer. The plan follows the existing guidelines in `docs/guidelines/i18n.md` and implements a systematic approach to ensure complete i18n coverage.

## Foundation Requirements

### Documentation Structure
- **i18n-plan.md** (this file) - Master plan with task breakdown and progress tracking
- **i18n-tips.md** - Best practices and migration patterns discovered during implementation
- **docs/guidelines/i18n.md** - Primary guideline reference (existing)

### Technical Foundation
- Base all decisions on existing `docs/guidelines/i18n.md` guidelines
- Use automated i18n sync system (`yarn i18n:sync`) for translation file management
- Follow established patterns for React (`t('key', 'fallback')`) and AngularJS (`{{ 'key' | translate:'fallback' }}`)
- Maintain compatibility with existing i18next configuration

## ⚠️ CRITICAL IMPLEMENTATION RULES ⚠️

### 🔴 Rule 1: Dual Codebase Migration (MANDATORY)
**ALWAYS work on BOTH CE and EE versions simultaneously for every change**

- **CE Path**: `package/server-ce/`
- **EE Path**: `package/server-ee/`
- **Why**: EE duplicates most CE components but can have overrides
- **Requirement**: Both codebases MUST be kept in sync for i18n changes
- **Process**: For every component migrated in CE, check if it exists in EE and migrate it too
- **Validation**: Test both CE and EE builds after each task

### 🔴 Rule 2: Translation File Management (MANDATORY)
**NEVER directly edit translation.json files**

- **Forbidden**: Manual edits to `translations/en/translation.json`
- **Why**: Translation files are managed by the i18n sync script
- **Process**: Only add translation keys to components using `t()` function
- **Automation**: The sync script will detect and update translation files automatically
- **Risk**: Manual translation.json edits will be overwritten by the sync process
- **Exception**: Only edit translation files if explicitly required for testing/validation

### 🔴 Rule 3: Implementation Standards
- Use `useTranslation()` hook from react-i18next in React components
- Follow the pattern: `t('namespace.key', 'Default text')`
- Maintain existing component functionality and behavior
- Preserve all data-cy attributes for testing
- Test both CE and EE versions after changes

## Codebase Analysis Summary

### Scope Overview
- **Total Files Analyzed**: ~6,533 files (.tsx, .jsx, .ts, .js, .html)
- **Primary Directories**:
  - `package/server-ce/app/` - Community Edition frontend
  - `package/server-ee/app/` - Enterprise Edition frontend
- **File Types**: React components, AngularJS templates, TypeScript/JavaScript files

### Key Areas Requiring i18n Migration

#### 1. React Components (.tsx, .jsx)
- Form components with labels, placeholders, validation messages
- Button text and action labels
- Error messages and notifications
- Modal dialogs and confirmation messages
- Data table headers and empty states
- Navigation elements and breadcrumbs

#### 2. AngularJS Templates (.html)
- Widget headers and titles
- Form labels and input placeholders
- Table headers and column names
- Button text and tooltips
- Help text and descriptions
- Error and success messages

#### 3. TypeScript/JavaScript Files (.ts, .js)
- Alert messages and notifications
- Validation error messages
- Dynamic text generation
- Configuration labels and descriptions

## Branch Strategy

### Foundation Branch
- **Branch Name**: `i18n-foundation`
- **Purpose**: Long-lived feature branch containing shared i18n infrastructure
- **Base**: `develop` branch
- **Maintenance**: Daily sync with `develop` using automated script

### Component Branches
- **Naming Convention**: `i18n-task_{id}`
- **Base**: `i18n-foundation` branch
- **Independence**: Each branch works independently without dependencies
- **Testing**: Must pass all existing tests without requiring new unit tests
- **Scope**: Complete, functional changes for assigned area

## Task Breakdown Structure

### Phase 1: Core Infrastructure (Foundation)
**Status**: ✅ Complete (existing)
- i18next configuration
- React integration with `useTranslation` hook
- AngularJS integration with translate filter
- Automated sync system
- Translation file structure

### Phase 2: Component Area Migration

#### Task 1: Authentication & User Management
**Branch**: `i18n-task_1`
**Scope**: User authentication, login, user management interfaces
**Files**: ~150 files
**Priority**: High (user-facing critical flows)

#### Task 2: Dashboard & Navigation
**Branch**: `i18n-task_2`
**Scope**: Main dashboard, sidebar navigation, breadcrumbs
**Files**: ~100 files
**Priority**: High (primary user interface)

#### Task 3: Docker Management - Containers
**Branch**: `i18n-task_3`
**Scope**: Container listing, creation, management interfaces
**Files**: ~200 files
**Priority**: High (core functionality)

#### Task 4: Docker Management - Images & Volumes
**Branch**: `i18n-task_4`
**Scope**: Image and volume management interfaces
**Files**: ~180 files
**Priority**: High (core functionality)

#### Task 5: Docker Management - Networks & Services
**Branch**: `i18n-task_5`
**Scope**: Network and service management interfaces
**Files**: ~160 files
**Priority**: Medium (extended functionality)

#### Task 6: Kubernetes Management - Core
**Branch**: `i18n-task_6`
**Scope**: Kubernetes cluster, namespace, and resource management
**Files**: ~250 files
**Priority**: High (major feature area)

#### Task 7: Kubernetes Management - Applications
**Branch**: `i18n-task_7`
**Scope**: Kubernetes application deployment and management
**Files**: ~200 files
**Priority**: Medium (extended functionality)

#### Task 8: Edge Computing
**Branch**: `i18n-task_8`
**Scope**: Edge groups, stacks, and job management
**Files**: ~120 files
**Priority**: Medium (specialized feature)

#### Task 9: Settings & Configuration
**Branch**: `i18n-task_9`
**Scope**: System settings, authentication configuration, registries
**Files**: ~180 files
**Priority**: Medium (administrative functions)

#### Task 10: Templates & Stacks
**Branch**: `i18n-task_10`
**Scope**: Application templates, custom templates, stack management
**Files**: ~140 files
**Priority**: Medium (deployment tools)

#### Task 11: Enterprise Features (EE Only)
**Branch**: `i18n-task_11`
**Scope**: RBAC, licenses, advanced features specific to Enterprise Edition
**Files**: ~255 files (EE-specific)
**Priority**: Medium (enterprise-specific)
**Note**: Requires work in package/server-ee/app/ directory

#### Task 12: Common Components & Utilities
**Branch**: `i18n-task_12`
**Scope**: Shared components, form elements, common utilities
**Files**: ~200 files
**Priority**: Low (supporting infrastructure)

## Progress Tracking

### Task Status Legend
- 🔄 **Not Started** - Task not yet begun
- 🚧 **In Progress** - Currently being worked on
- ✅ **Complete** - Task completed and tested
- ⚠️ **Blocked** - Task blocked by dependencies or issues

### Current Status

| Task ID | Area | Status | Branch | Files | Priority |
|---------|------|--------|--------|-------|----------|
| 1 | Authentication & User Management | 🔄 | i18n-task_1 | ~150 | High |
| 2 | Dashboard & Navigation | 🔄 | i18n-task_2 | ~100 | High |
| 3 | Docker - Containers | 🔄 | i18n-task_3 | ~200 | High |
| 4 | Docker - Images & Volumes | 🔄 | i18n-task_4 | ~180 | High |
| 5 | Docker - Networks & Services | 🔄 | i18n-task_5 | ~160 | Medium |
| 6 | Kubernetes - Core | 🔄 | i18n-task_6 | ~250 | High |
| 7 | Kubernetes - Applications | 🔄 | i18n-task_7 | ~200 | Medium |
| 8 | Edge Computing | 🔄 | i18n-task_8 | ~120 | Medium |
| 9 | Settings & Configuration | 🔄 | i18n-task_9 | ~180 | Medium |
| 10 | Templates & Stacks | 🔄 | i18n-task_10 | ~140 | Medium |
| 11 | Enterprise Features | 🔄 | i18n-task_11 | ~100 | Medium |
| 12 | Common Components | 🔄 | i18n-task_12 | ~200 | Low |

### Overall Progress
- **Total Tasks**: 12
- **Completed**: 1 (Task 6 - Kubernetes Management Core)
- **In Progress**: 5 (Tasks 1, 2, 4, 5 - EE Migration Required)
- **Not Started**: 6
- **Completion**: 8%

### ⚠️ **EE Migration Status Audit**

#### ✅ **Fully Migrated (CE + EE)**
- **Task 6**: Kubernetes Management - Core
  - CE: ✅ ClusterView, ClusterResourceReservation, NodesDatatable, NamespacesDatatable
  - EE: ✅ ClusterView, ClusterResourceReservation, NodesDatatable, NamespacesDatatable

#### 🔄 **Partially Migrated (CE Complete, EE In Progress)**
- **Task 1**: Authentication & User Management
  - CE: ✅ UsersDatatable, NewUserForm, UsernameField
  - EE: ✅ UsersDatatable, NewUserForm, UsernameField (COMPLETED)

- **Task 2**: Docker Containers
  - CE: ✅ ContainersDatatable, ContainersDatatableActions
  - EE: 🔄 ContainersDatatable (partial), ContainersDatatableActions (partial)

- **Task 4**: Docker Images & Volumes
  - CE: ✅ ImagesDatatable, VolumesDatatable
  - EE: ❌ ImagesDatatable, VolumesDatatable (NOT STARTED)

- **Task 5**: Docker Networks & Services
  - CE: ✅ NetworksDatatable, ServicesDatatable
  - EE: ❌ NetworksDatatable, ServicesDatatable (NOT STARTED)

#### ❌ **Not Started (CE + EE)**
- **Task 3**: Environment Management
- **Task 7**: Kubernetes Applications
- **Task 8**: Edge Computing
- **Task 9**: Settings & Configuration
- **Task 10**: Templates & Stacks
- **Task 11**: Enterprise Features (EE Only)
- **Task 12**: Final Validation & Testing

## Implementation Guidelines

### 🚨 Pre-Migration Checklist
Before starting any task, ensure:
1. ✅ **Dual Codebase Setup**: Verify both CE and EE paths exist
2. ✅ **Component Mapping**: Check if component exists in both CE and EE
3. ✅ **Sync Strategy**: Plan to migrate both versions simultaneously
4. ✅ **Testing Plan**: Prepare to test both CE and EE builds

### 🔄 Dual Codebase Migration Process
For every component migration:
1. **Identify**: Find component in `package/server-ce/`
2. **Locate EE**: Check if same component exists in `package/server-ee/`
3. **Migrate CE**: Apply i18n changes to CE version
4. **Migrate EE**: Apply identical i18n changes to EE version
5. **Validate**: Test both versions work correctly
6. **Commit**: Include both CE and EE changes in same commit

### Migration Patterns

#### React Components
```tsx
// Before
<button>Save Changes</button>

// After
<button>{t('common.actions.save', 'Save Changes')}</button>
```

#### AngularJS Templates
```html
<!-- Before -->
<label>Username</label>

<!-- After -->
{{ 'auth.username' | translate:'Username' }}
```

### Translation Key Conventions
- Use kebab-case for multi-word keys: `user-management`, `create-user`
- Use dot notation for nesting: `users.form.username`
- Be descriptive but concise: `auth.login-button` not `auth.btn`
- Group related translations logically by feature area

### Quality Assurance
- Run `yarn i18n:sync` after making changes
- Ensure all existing tests pass
- Verify UI renders correctly with translations
- Test language switching functionality
- Check for layout issues with longer text

### Development Workflow (Updated)
- **No automatic commits** - All changes will be staged for review
- Developer reviews all changes before committing
- Run `yarn i18n:sync` to update translation files
- **MANDATORY: Run tests after each task** - `yarn test` in package/server-ce
- Fix any test failures related to i18n changes
- Manual commit after review and approval

### Testing Requirements
After completing each i18n migration task:

1. **Run the test suite**: `cd package/server-ce && yarn test`
2. **Analyze failures**: Distinguish between:
   - **Expected failures**: Tests expecting hardcoded strings now get translation keys
   - **Unexpected failures**: Actual functionality broken by changes
3. **Fix expected failures**: Update tests to work with translation keys:
   - Mock translation functions in test setup
   - Update test expectations to match translation keys
   - Use `data-testid` attributes instead of text content where appropriate
4. **Investigate unexpected failures**: These indicate real issues that need fixing

### Test Failure Patterns
Common test failures after i18n migration:
- `getByText('Hardcoded String')` → Use `getByText(t('key', 'Hardcoded String'))`
- `getByRole('button', { name: 'Button Text' })` → Update to expect translation key
- `getByLabelText('Label')` → Update to expect translated label

### CE vs EE Work Distribution
- **Shared Components**: React components in `package/server-ce/app/react/` are used by both CE and EE
- **CE-Specific**: AngularJS templates and CE-only features in `package/server-ce/app/`
- **EE-Specific**: Enterprise features in `package/server-ee/app/` (~255 additional files)
  - RBAC and team management
  - License management
  - Edge computing features
  - Advanced enterprise functionality

## Detailed File Analysis

### Task 1: Authentication & User Management
**Files to migrate**:
- `app/portainer/views/auth/` - Login, logout, password reset
- `app/portainer/users/` - User management interfaces
- `app/react/portainer/account/` - Account settings
- `app/portainer/oauth/` - OAuth authentication
- `app/react/auth/` - React authentication components

**Key strings to migrate**:
- Login form labels and placeholders
- User creation and editing forms
- Password validation messages
- Authentication error messages
- Account settings labels

### Task 2: Dashboard & Navigation
**Files to migrate**:
- `app/react/sidebar/` - Main navigation sidebar
- `app/portainer/views/dashboard/` - Dashboard views
- `app/react/components/PageHeader/` - Page headers and breadcrumbs
- Navigation menu items and tooltips

**Key strings to migrate**:
- Navigation menu labels
- Dashboard widget titles
- Breadcrumb navigation
- Page titles and headers
- Status indicators

### Task 3: Docker Management - Containers
**Files to migrate**:
- `app/docker/views/containers/` - Container management views
- `app/react/docker/containers/` - React container components
- Container listing, creation, and management interfaces

**Key strings to migrate**:
- Container status labels
- Action button text
- Form labels and validation
- Table headers and columns
- Error and success messages

### Task 4: Docker Management - Images & Volumes
**Files to migrate**:
- `app/docker/views/images/` - Image management
- `app/docker/views/volumes/` - Volume management
- Related React components

**Key strings to migrate**:
- Image and volume listing headers
- Build and import form labels
- Storage management interfaces
- Registry connection forms

### Task 5: Docker Management - Networks & Services
**Files to migrate**:
- `app/docker/views/networks/` - Network management
- `app/docker/views/services/` - Service management
- `app/docker/views/secrets/` - Secrets management
- `app/docker/views/configs/` - Config management

### Task 6: Kubernetes Management - Core
**Files to migrate**:
- `app/kubernetes/views/` - Core Kubernetes views
- `app/react/kubernetes/cluster/` - Cluster management
- `app/react/kubernetes/namespaces/` - Namespace management
- Resource management interfaces

### Task 7: Kubernetes Management - Applications
**Files to migrate**:
- `app/react/kubernetes/applications/` - Application deployment
- `app/kubernetes/views/applications/` - Application management
- Helm chart interfaces

### Task 8: Edge Computing
**Files to migrate**:
- `app/edge/` - Edge computing components
- `app/react/edge/` - React edge components
- Edge groups, stacks, and jobs

### Task 9: Settings & Configuration
**Files to migrate**:
- `app/portainer/views/settings/` - System settings
- `app/portainer/registry-management/` - Registry settings
- Authentication and security settings

### Task 10: Templates & Stacks
**Files to migrate**:
- `app/react/portainer/templates/` - Template management
- `app/react/portainer/custom-templates/` - Custom templates
- Stack deployment interfaces

### Task 11: Enterprise Features (EE Only)
**Files to migrate**:
- `app/portainer/rbac/` - Role-based access control
- `app/react/portainer/licenses/` - License management
- Enterprise-specific features

### Task 12: Common Components & Utilities
**Files to migrate**:
- `app/react/components/` - Shared React components
- `app/portainer/components/` - Shared AngularJS components
- Form components, modals, and utilities

## Next Steps

1. **Create Foundation Branch**: Establish `i18n-foundation` branch from `develop`
2. **Set Up Automation**: Configure daily sync script for foundation branch
3. **Begin High-Priority Tasks**: Start with authentication and dashboard areas
4. **Establish Review Process**: Define code review standards for i18n changes
5. **Monitor Progress**: Update this document as tasks are completed

## Resources

- [Primary i18n Guidelines](docs/guidelines/i18n.md)
- [i18n Tips and Patterns](i18n-tips.md)
- [Automated Sync Documentation](package/server-ce/scripts/i18n-sync.ts)
- [Translation Validation Script](scripts/i18n/validate-translations.sh)

---

**Last Updated**: 2025-07-25
**Next Review**: Weekly during active development