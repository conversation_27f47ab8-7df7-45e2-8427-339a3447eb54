# i18n Migration Tips and Best Practices

## Overview

This document captures best practices, migration patterns, and solutions discovered during the i18n implementation process. It serves as a living reference for developers working on i18n migration tasks.

## Migration Patterns

### React Components

#### Basic Text Replacement
```tsx
// Before
<h1>Dashboard</h1>

// After
import { useTranslation } from 'react-i18next';

function Dashboard() {
  const { t } = useTranslation();
  return <h1>{t('dashboard.title', 'Dashboard')}</h1>;
}
```

#### Form Labels and Placeholders
```tsx
// Before
<input
  type="text"
  placeholder="Enter username"
  aria-label="Username field"
/>

// After
<input
  type="text"
  placeholder={t('auth.username-placeholder', 'Enter username')}
  aria-label={t('auth.username-label', 'Username field')}
/>
```

#### Button Text with Actions
```tsx
// Before
<button onClick={handleSave}>Save Changes</button>
<button onClick={handleCancel}>Cancel</button>

// After
<button onClick={handleSave}>
  {t('common.actions.save-changes', 'Save Changes')}
</button>
<button onClick={handleCancel}>
  {t('common.actions.cancel', 'Cancel')}
</button>
```

#### Conditional Text
```tsx
// Before
<span>{isLoading ? 'Loading...' : 'Load More'}</span>

// After
<span>
  {isLoading
    ? t('common.loading', 'Loading...')
    : t('common.load-more', 'Load More')
  }
</span>
```

#### Error Messages
```tsx
// Before
const errorMessage = 'Invalid username or password';

// After
const { t } = useTranslation();
const errorMessage = t('auth.invalid-credentials', 'Invalid username or password');
```

### AngularJS Templates

#### Basic Text Replacement
```html
<!-- Before -->
<h1>User Management</h1>

<!-- After -->
<h1>{{ 'users.title' | translate:'User Management' }}</h1>
```

#### Form Labels
```html
<!-- Before -->
<label for="username">Username</label>

<!-- After -->
<label for="username">{{ 'auth.username' | translate:'Username' }}</label>
```

#### Button Text
```html
<!-- Before -->
<button class="btn btn-primary">Create User</button>

<!-- After -->
<button class="btn btn-primary">
  {{ 'users.actions.create' | translate:'Create User' }}
</button>
```

#### Table Headers
```html
<!-- Before -->
<th>Name</th>
<th>Status</th>
<th>Actions</th>

<!-- After -->
<th>{{ 'common.name' | translate:'Name' }}</th>
<th>{{ 'common.status' | translate:'Status' }}</th>
<th>{{ 'common.actions.title' | translate:'Actions' }}</th>
```

#### Tooltips and Help Text
```html
<!-- Before -->
<portainer-tooltip message="'Click to edit user details'"></portainer-tooltip>

<!-- After -->
<portainer-tooltip message="'users.edit-tooltip' | translate:'Click to edit user details'"></portainer-tooltip>
```

### JavaScript/TypeScript Files

#### Alert Messages
```typescript
// Before
alert('User created successfully');

// After
import { i18n } from '@/i18n';
alert(i18n.t('users.create-success', 'User created successfully'));
```

#### Dynamic Messages
```typescript
// Before
const message = `Container ${containerName} started successfully`;

// After
const message = t('containers.start-success', 'Container {{name}} started successfully', {
  name: containerName
});
```

## Translation Key Organization

### Hierarchical Structure
```json
{
  "common": {
    "actions": {
      "save": "Save",
      "cancel": "Cancel",
      "delete": "Delete",
      "edit": "Edit",
      "create": "Create"
    },
    "status": {
      "active": "Active",
      "inactive": "Inactive",
      "pending": "Pending"
    }
  },
  "auth": {
    "login": "Login",
    "logout": "Logout",
    "username": "Username",
    "password": "Password",
    "forgot-password": "Forgot Password?"
  },
  "users": {
    "title": "Users",
    "create-user": "Create User",
    "edit-user": "Edit User",
    "delete-user": "Delete User"
  }
}
```

### Key Naming Conventions
- Use kebab-case: `create-user`, `forgot-password`
- Be descriptive: `auth.login-button` not `auth.btn`
- Group by feature: `users.*`, `containers.*`, `settings.*`
- Use consistent patterns: `*.title`, `*.actions.*`, `*.form.*`

## Common Challenges and Solutions

### Challenge 1: Dynamic Content
**Problem**: Text that includes dynamic values
```tsx
// Problematic
<span>You have {count} items</span>
```

**Solution**: Use interpolation
```tsx
// Correct
<span>{t('common.item-count', 'You have {{count}} items', { count })}</span>
```

### Challenge 2: Pluralization
**Problem**: Handling singular/plural forms
```tsx
// Problematic
<span>{count === 1 ? '1 item' : `${count} items`}</span>
```

**Solution**: Use i18next pluralization
```json
{
  "items": {
    "count_one": "{{count}} item",
    "count_other": "{{count}} items"
  }
}
```
```tsx
<span>{t('items.count', { count })}</span>
```

### Challenge 3: HTML Content in Translations
**Problem**: Translations containing HTML markup
```tsx
// Problematic
<p>Click <a href="/help">here</a> for help</p>
```

**Solution**: Use interpolation with components
```tsx
<p>
  {t('help.click-here', 'Click {{link}} for help', {
    link: <a href="/help">{t('help.here', 'here')}</a>
  })}
</p>
```

### Challenge 4: AngularJS Controller Text
**Problem**: Text generated in controllers
```javascript
// Problematic
$scope.message = 'Operation completed successfully';
```

**Solution**: Access global i18n instance
```javascript
// Correct
$scope.message = window.i18n?.t('common.operation-success', 'Operation completed successfully') || 'Operation completed successfully';
```

## Testing Considerations

### Unit Tests
- Mock the translation function in tests
- Test with different language scenarios
- Verify fallback text is used when keys are missing

### Integration Tests
- Test language switching functionality
- Verify all UI elements render correctly
- Check for layout issues with longer translations

## Performance Tips

### Lazy Loading
- Load translation files on demand for large applications
- Use namespace splitting for better performance

### Caching
- Leverage browser caching for translation files
- Use service workers for offline translation support

## Automation Tools

### i18n Sync Script
```bash
# Run after making changes
yarn i18n:sync

# Preview changes without applying
yarn i18n:sync --dry-run

# Check for missing translations
yarn i18n:sync --check-only
```

### Validation Script
```bash
# Validate all translations
./scripts/i18n/validate-translations.sh

# Check for unused keys
grep -r "t('unused.key')" package/server-ce/app/
```

## Common Mistakes to Avoid

1. **Hardcoded Strings**: Always use translation functions, never hardcode user-facing text
2. **Missing Fallbacks**: Always provide fallback text for translation keys
3. **Inconsistent Keys**: Use consistent naming patterns across the application
4. **Overly Generic Keys**: Be specific about context (`users.delete` vs `common.delete`)
5. **Missing Interpolation**: Don't concatenate strings, use interpolation instead

## Review Checklist

Before submitting i18n changes:
- [ ] All user-facing strings use translation functions
- [ ] Fallback text is provided for all keys
- [ ] Translation keys follow naming conventions
- [ ] `yarn i18n:sync` has been run
- [ ] All existing tests pass
- [ ] UI renders correctly with translations
- [ ] No hardcoded strings remain in the code

---

**Last Updated**: 2025-07-25
**Contributors**: Development team working on i18n migration