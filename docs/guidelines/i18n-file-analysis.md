# Detailed File Analysis for i18n Migration

## Overview

This document provides a comprehensive analysis of specific files that require i18n migration, organized by task areas. Each section includes concrete examples of hardcoded strings and their proposed translation keys.

## Task 1: Authentication & User Management

### Priority Files for Migration

#### 1. `package/server-ce/app/portainer/views/auth/auth.html`
**Hardcoded strings identified:**
- "Log in to your account" → `auth.login-title`
- "Welcome back! Please enter your details" → `auth.welcome-message`
- "Login with Microsoft" → `auth.login-with-microsoft`
- "Login with Google" → `auth.login-with-google`
- "Login with GitHub" → `auth.login-with-github`
- "Login with OAuth" → `auth.login-with-oauth`
- "or" → `common.or`
- "Use internal authentication" → `auth.use-internal-auth`
- "Username" → `auth.username`
- "Enter your username" → `auth.username-placeholder`
- "Password" → `auth.password`
- "Enter your password" → `auth.password-placeholder`
- "Show password" → `auth.show-password`
- "Hide password" → `auth.hide-password`

**Migration example:**
```html
<!-- Before -->
<p class="text-xl">Log in to your account</p>

<!-- After -->
<p class="text-xl">{{ 'auth.login-title' | translate:'Log in to your account' }}</p>
```

#### 2. `package/server-ce/app/portainer/users/` directory
**Key files:**
- User creation forms
- User editing interfaces
- User listing tables
- Role assignment components

#### 3. `package/server-ce/app/react/portainer/account/` directory
**Key files:**
- Account settings forms
- Password change interfaces
- Profile management

### Estimated Translation Keys: ~80-100 keys

## Task 2: Dashboard & Navigation

### Priority Files for Migration

#### 1. `package/server-ce/app/react/sidebar/` directory
**Key components:**
- Navigation menu items
- Sidebar headers and sections
- Environment switcher
- User menu dropdown

#### 2. Dashboard components
**Key files:**
- Main dashboard widgets
- Status indicators
- Quick action buttons
- Statistics displays

### Estimated Translation Keys: ~60-80 keys

## Task 3: Docker Management - Containers

### Priority Files for Migration

#### 1. Container listing and management
**Key areas:**
- Container status labels (Running, Stopped, Paused, etc.)
- Action buttons (Start, Stop, Restart, Remove, etc.)
- Table headers (Name, Image, Status, Ports, etc.)
- Filter and search placeholders

#### 2. Container creation forms
**Key areas:**
- Form field labels
- Validation messages
- Help text and tooltips
- Configuration sections

### Example Migration Pattern:
```tsx
// Before
<button onClick={handleStart}>Start</button>

// After
<button onClick={handleStart}>
  {t('containers.actions.start', 'Start')}
</button>
```

### Estimated Translation Keys: ~120-150 keys

## Task 4: Docker Management - Images & Volumes

### Priority Files for Migration

#### 1. Image management interfaces
**Key areas:**
- Image listing tables
- Build image forms
- Import image dialogs
- Registry connection forms

#### 2. Volume management interfaces
**Key areas:**
- Volume creation forms
- Volume listing and details
- Mount point configurations
- Storage driver options

### Estimated Translation Keys: ~100-120 keys

## Task 5: Docker Management - Networks & Services

### Priority Files for Migration

#### 1. Network management
**Key areas:**
- Network creation forms
- Network driver options
- IP address management
- Network connectivity settings

#### 2. Service management
**Key areas:**
- Service deployment forms
- Service scaling options
- Update configurations
- Health check settings

#### 3. Secrets and Configs
**Key areas:**
- Secret creation forms
- Config management interfaces
- Access control settings

### Estimated Translation Keys: ~90-110 keys

## Task 6: Kubernetes Management - Core

### Priority Files for Migration

#### 1. Cluster management
**Key areas:**
- Cluster connection forms
- Cluster status displays
- Node management interfaces
- Resource quotas and limits

#### 2. Namespace management
**Key areas:**
- Namespace creation forms
- Resource allocation settings
- Access control configurations

#### 3. Resource management
**Key areas:**
- Pod management interfaces
- Service configurations
- Ingress settings
- ConfigMap and Secret management

### Estimated Translation Keys: ~150-180 keys

## Common String Patterns Across All Tasks

### Action Buttons
```json
{
  "common": {
    "actions": {
      "create": "Create",
      "edit": "Edit",
      "delete": "Delete",
      "save": "Save",
      "cancel": "Cancel",
      "start": "Start",
      "stop": "Stop",
      "restart": "Restart",
      "remove": "Remove",
      "update": "Update",
      "deploy": "Deploy",
      "connect": "Connect",
      "disconnect": "Disconnect"
    }
  }
}
```

### Status Labels
```json
{
  "common": {
    "status": {
      "running": "Running",
      "stopped": "Stopped",
      "paused": "Paused",
      "pending": "Pending",
      "active": "Active",
      "inactive": "Inactive",
      "healthy": "Healthy",
      "unhealthy": "Unhealthy"
    }
  }
}
```

### Form Labels
```json
{
  "common": {
    "form": {
      "name": "Name",
      "description": "Description",
      "image": "Image",
      "port": "Port",
      "volume": "Volume",
      "network": "Network",
      "environment": "Environment",
      "labels": "Labels",
      "constraints": "Constraints"
    }
  }
}
```

### Table Headers
```json
{
  "common": {
    "table": {
      "name": "Name",
      "status": "Status",
      "actions": "Actions",
      "created": "Created",
      "updated": "Updated",
      "size": "Size",
      "type": "Type"
    }
  }
}
```

## File Organization Strategy

### By Feature Area
```
translations/
├── en/
│   └── translation.json
│       ├── common/          # Shared across all features
│       ├── auth/            # Authentication related
│       ├── users/           # User management
│       ├── dashboard/       # Dashboard and navigation
│       ├── containers/      # Container management
│       ├── images/          # Image management
│       ├── volumes/         # Volume management
│       ├── networks/        # Network management
│       ├── services/        # Service management
│       ├── kubernetes/      # Kubernetes management
│       ├── edge/            # Edge computing
│       ├── settings/        # Settings and configuration
│       └── templates/       # Templates and stacks
```

## Migration Priority Matrix

| Task | Priority | Complexity | User Impact | Estimated Effort |
|------|----------|------------|-------------|------------------|
| 1. Auth & Users | High | Medium | High | 2-3 weeks |
| 2. Dashboard | High | Low | High | 1-2 weeks |
| 3. Containers | High | High | High | 3-4 weeks |
| 4. Images & Volumes | High | Medium | Medium | 2-3 weeks |
| 5. Networks & Services | Medium | Medium | Medium | 2-3 weeks |
| 6. Kubernetes Core | High | High | High | 4-5 weeks |
| 7. Kubernetes Apps | Medium | High | Medium | 3-4 weeks |
| 8. Edge Computing | Medium | Medium | Low | 2-3 weeks |
| 9. Settings | Medium | Medium | Medium | 2-3 weeks |
| 10. Templates | Medium | Medium | Medium | 2-3 weeks |
| 11. Enterprise | Medium | Medium | Low | 2-3 weeks |
| 12. Common Components | Low | High | High | 3-4 weeks |

## Quality Assurance Checklist

For each task completion:
- [ ] All hardcoded strings replaced with translation functions
- [ ] Translation keys follow naming conventions
- [ ] Fallback text provided for all keys
- [ ] `yarn i18n:sync` executed successfully
- [ ] All existing tests pass
- [ ] UI renders correctly with translations
- [ ] Language switching works properly
- [ ] No layout issues with longer text
- [ ] Accessibility attributes translated
- [ ] Error messages and validation text translated

## Next Steps

1. **Begin with Task 1 (Authentication)** - Highest user impact and visibility
2. **Establish review process** - Define code review standards for i18n changes
3. **Create branch structure** - Set up `i18n-foundation` and task branches
4. **Set up automation** - Configure daily sync and validation scripts
5. **Monitor progress** - Update tracking documents as tasks are completed

---

**Last Updated**: 2025-07-25
**Next Review**: Weekly during active development