# Internationalization (i18n) Guidelines

This guide explains how to implement internationalization in <PERSON><PERSON><PERSON>'s hybrid React/AngularJS codebase.

## Overview

Portainer uses **i18next** as the core internationalization library with custom integrations for both React and AngularJS components. All translations are stored in static JSON files and loaded at runtime.

**🚀 New: Automated i18n Sync System**
Developers no longer need to manually edit translation files! Use the automated sync system by writing i18n functions with fallback text. See the [i18n Sync System Documentation](../i18n-sync-system.md) for complete details.

## Quick Start with Automated Sync

1. **Write code with fallback text:**
   ```tsx
   // React
   const { t } = useTranslation();
   return <h1>{t('dashboard.title', 'Dashboard')}</h1>;
   ```

   ```html
   <!-- AngularJS -->
   <h1>{{ 'dashboard.title' | translate:'Dashboard' }}</h1>
   ```

2. **Run the sync script:**
   ```bash
   yarn i18n:sync
   ```

3. **Translation files are automatically updated!**

## Translation Files Structure

Translation files are located in:
- `package/server-ce/translations/[language]/translation.json`
- `package/server-ee/translations/[language]/translation.json`

Currently supported languages:
- `en` - English (default)
- `cn` - Chinese (Simplified)

### Translation Key Structure

Use nested objects to organize translation keys logically:

```json
{
  "navigation": {
    "home": "Home",
    "dashboard": "Dashboard"
  },
  "users": {
    "title": "Users",
    "actions": {
      "create": "Create User",
      "delete": "Delete User"
    },
    "form": {
      "username": "Username",
      "email": "Email Address"
    }
  }
}
```

## React Components

### Basic Usage

Import and use the `useTranslation` hook:

```tsx
import { useTranslation } from 'react-i18next';

export function MyComponent() {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t('users.title')}</h1>
      <button>{t('users.actions.create')}</button>
    </div>
  );
}
```

### With Interpolation

Pass variables to translation strings:

```tsx
// Translation file
{
  "users": {
    "welcome": "Welcome, {{username}}!",
    "itemCount": "You have {{count}} items"
  }
}

// React component
export function WelcomeMessage({ username, itemCount }) {
  const { t } = useTranslation();

  return (
    <div>
      <p>{t('users.welcome', { username })}</p>
      <p>{t('users.itemCount', { count: itemCount })}</p>
    </div>
  );
}
```

### With Fallback Values

Provide fallback text for missing translations:

```tsx
export function MyComponent() {
  const { t } = useTranslation();

  return (
    <h1>{t('users.title', 'Users')}</h1>
  );
}
```

### Pluralization

Handle singular/plural forms:

```tsx
// Translation file
{
  "items": {
    "count_one": "{{count}} item",
    "count_other": "{{count}} items"
  }
}

// React component
export function ItemList({ items }) {
  const { t } = useTranslation();

  return (
    <p>{t('items.count', { count: items.length })}</p>
  );
}
```

## AngularJS Components

### Using the Translate Filter

Use the `translate` filter in templates:

```html
<!-- Basic usage -->
<h1>{{ 'users.title' | translate }}</h1>

<!-- With fallback -->
<h1>{{ 'users.title' | translate:'Users' }}</h1>

<!-- With interpolation -->
<p>{{ 'users.welcome' | translate:'{username: user.name}' }}</p>
```

### Using the I18nService

Inject and use the `I18nService` in controllers:

```javascript
angular.module('myModule').controller('MyController', MyController);

function MyController(I18nService) {
  'ngInject';
  
  const vm = this;
  
  // Get current language
  vm.currentLanguage = I18nService.getCurrentLanguage();
  
  // Change language
  vm.changeLanguage = function(language) {
    I18nService.changeLanguage(language);
  };
  
  // Listen for language changes
  $scope.$on('languageChanged', function(event, newLanguage) {
    // Handle language change
    vm.currentLanguage = newLanguage;
  });
}
```

### Dynamic Translation in Controllers

For dynamic translations in JavaScript code:

```javascript
function MyController($scope) {
  'ngInject';
  
  const vm = this;
  
  // Access global i18n instance
  vm.getTranslation = function(key, fallback) {
    if (window.i18n && window.i18n.t) {
      return window.i18n.t(key) || fallback;
    }
    return fallback;
  };
  
  vm.showMessage = function() {
    const message = vm.getTranslation('users.success', 'Operation successful');
    // Use the translated message
  };
}
```

## Best Practices

### 1. Key Naming Conventions

- Use **kebab-case** for multi-word keys: `user-management`, `create-user`
- Use **dot notation** for nesting: `users.form.username`
- Be descriptive but concise: `auth.login-button` not `auth.btn`

### 2. Organizing Translation Keys

Group related translations logically:

```json
{
  "navigation": { /* navigation items */ },
  "auth": { /* authentication related */ },
  "users": { /* user management */ },
  "containers": { /* container management */ },
  "common": {
    "actions": { /* common actions like save, cancel */ },
    "messages": { /* common messages like success, error */ }
  }
}
```

### 3. Fallback Strategy

Always provide fallback text:

```tsx
// React
{t('some.key', 'Default text')}

// AngularJS
{{ 'some.key' | translate:'Default text' }}
```

### 4. Avoid Hardcoded Strings

❌ **Don't do this:**
```tsx
<button>Save Changes</button>
```

✅ **Do this:**
```tsx
<button>{t('common.actions.save', 'Save Changes')}</button>
```

### 5. Context-Specific Keys

Use specific keys rather than generic ones:

❌ **Don't do this:**
```json
{
  "save": "Save",
  "delete": "Delete"
}
```

✅ **Do this:**
```json
{
  "users": {
    "save-user": "Save User",
    "delete-user": "Delete User"
  }
}
```

## Language Settings UI

Users can change their language preference through the Language Settings widget in their account settings. This widget:

- Displays available languages
- Saves preference to localStorage
- Updates the interface immediately
- Syncs between React and AngularJS components

## Adding New Languages

1. **Create translation files:**
   ```bash
   # Add new language files
   touch package/server-ce/translations/[lang]/translation.json
   touch package/server-ee/translations/[lang]/translation.json
   ```

2. **Update i18n configuration:**
   ```typescript
   // In i18n.ts
   supportedLngs: ['en', 'cn', 'newlang'],
   
   resources: {
     en: { translation: enTranslations },
     cn: { translation: cnTranslations },
     newlang: { translation: newlangTranslations },
   }
   ```

3. **Update language options:**
   ```typescript
   // In LanguageSettingsForm.tsx
   const LANGUAGE_OPTIONS: Option<string>[] = [
     { value: 'en', label: 'English' },
     { value: 'cn', label: '中文 (Chinese)' },
     { value: 'newlang', label: 'New Language' },
   ];
   ```

## Testing

### Testing React Components

```tsx
import { render } from '@testing-library/react';
import { I18nextProvider } from 'react-i18next';
import i18n from '../test-utils/i18n-test-setup';

test('renders translated text', () => {
  const { getByText } = render(
    <I18nextProvider i18n={i18n}>
      <MyComponent />
    </I18nextProvider>
  );
  
  expect(getByText('Users')).toBeInTheDocument();
});
```

### Testing AngularJS Components

Ensure the translate filter and I18nService are properly mocked in your tests.

## Troubleshooting

### Common Issues

1. **Translation not updating**: Check if the key exists in the translation file
2. **AngularJS not reflecting changes**: Ensure `$rootScope.$apply()` is called
3. **Missing translations**: Always provide fallback text
4. **Language not persisting**: Check localStorage for 'portainer-language' key

### Debug Mode

Enable debug mode in development:

```typescript
// In i18n.ts
debug: process.env.NODE_ENV === 'development'
```

This will log missing translation keys to the console.
