# Portainer i18n Migration Plan for JavaScript Controllers and TypeScript Files

This document provides a complete guide and plan for migrating JavaScript controllers and TypeScript files in the Portainer CE and EE codebases to full i18n support.

---

## ⚠️ CRITICAL IMPLEMENTATION RULES ⚠️

These rules are **mandatory** for all developers to follow to ensure a smooth and consistent i18n migration.

### 🔴 Rule 1: Work on Both Codebases Simultaneously
**ALWAYS work on BOTH CE and EE versions simultaneously for every change.**

-   **Paths**: `package/server-ce/` (CE) and `package/server-ee/` (EE)
-   **Why**: EE duplicates most CE components but can have overrides. Both codebases MUST be kept in sync for all i18n changes.
-   **Process**: For every file migrated in CE, check if it exists in EE and migrate it at the same time. Both files should be part of the same commit.

### 🔴 Rule 2: Manage Translation Files Properly
**NEVER directly edit `translation.json` files.**

-   **Why**: Translation files are managed by an automated script (`yarn i18n:sync`) that scans the code for keys. Manual edits will be overwritten.
-   **Process**: Only add translation keys within the code itself using the correct syntax for AngularJS controllers or TypeScript files. The automation will handle the rest.

### 🔴 Rule 3: Branch Management
**ALL work must be done on the `feat/i18n-foundation` branch.**

-   **Why**: Isolates i18n migration work from other development and ensures proper coordination.
-   **Process**: 
    1. Work exclusively on the `feat/i18n-foundation` branch for all i18n tasks
    2. ALWAYS fetch latest changes from `develop` branch and rebase `feat/i18n-foundation` before any modification:
        ```bash
        git fetch origin
        git rebase origin/develop
        ```
    3. WHEN conflicts occur during rebase, ALWAYS prioritize keeping our i18n implementation and avoid modifying any incoming changes:
        - Prioritize our i18n changes
        - Do NOT modify any incoming changes unrelated to i18n
        - After resolving conflicts, run type and lint checks again to ensure everything is working
        - Build both CE and EE projects to verify nothing is broken before continuing work

### 🔴 Rule 4: Verification Process
**Follow the proper verification sequence for quality assurance.**

-   **Why**: Ensures code quality while improving efficiency by reducing redundant verification runs.
-   **Process**: 
    1. DO NOT run unit tests for task or subtask verification (they can fail for unrelated reasons)
    2. ONLY run type check and lint check when a whole task is finished, not for each subtask
    3. After completing all subtasks for a task, run in sequence:
        - Lint checks for both CE and EE
        - Type checks for both CE and EE
        - Build verification for both CE and EE:
            ```bash
            # In package/server-ce/
            yarn build
            
            # In package/server-ee/
            yarn build
            ```
    4. DO NOT commit changes immediately when a task or subtask is finished - keep changes local until a logical group of work is complete and ready for review

### 🔴 Rule 5: Documentation and Tracking
**Maintain proper documentation and progress tracking.**

-   **Why**: Provides clear visibility of completed work and progress tracking.
-   **Process**: 
    1. ALWAYS mark tasks as done in the progress section when finished
    2. Update the task checklist in the "Detailed Migration Plan & Progress" section to reflect completed work
    3. Follow commit message pattern:
        ```bash
        feat(i18n): Task X - [Brief description]
        - Migrate [component names] with [number] translation keys
        - Add comprehensive [feature area] translation sections
        - [Other relevant details]
        ```

### 🔴 Rule 6: No Direct Commits
**DO NOT commit any changes directly to the repository during the migration process.**

-   **Why**: Ensures proper review and coordination of all changes before they are permanently added to the codebase.
-   **Process**: 
    1. Keep all changes local until instructed to commit
    2. All changes will be committed in bulk at the end of the migration process
    3. Follow the established commit message pattern when committing

### 🔴 Rule 7: Dynamic Task List Updates
**Update the task list dynamically when discovering new files or folders requiring i18n support.**

-   **Why**: Ensures comprehensive i18n coverage without missing any files or folders in server-ce and server-ee.
-   **Process**: 
    1. When working on a task, if you discover additional files or folders in the same location or module that require i18n support, add them to the current task list
    2. Continue working on the modifications for these newly discovered items as part of the current task
    3. This plan is a living document that should be updated as new items are found to ensure complete i18n implementation

---

## 📖 Development Guide & Workflow

This guide explains how to implement internationalization in Portainer's JavaScript controllers and TypeScript files.

### Step 1: Identify and Extract Hardcoded Strings

Locate all user-facing strings in the target files and replace them using the appropriate method for the file type.

#### **For AngularJS Controllers (`.js` files)**

Use the `$translate` service to replace hardcoded strings in notifications and error messages.

1.  **Inject the `$translate` service** in the controller constructor and function signature:
    ```javascript
    class ExampleController {
      /* @ngInject */
      constructor($scope, $translate) {  // Add $translate to constructor parameters
        this.$translate = $translate;    // Assign to class property
        // Other constructor code
      }
      
      someMethod() {
        // Use $translate.instant() for translations
      }
    }
    
    // Make sure $translate is in the dependency array
    angular.module('portainer.app').controller('ExampleController', ExampleController);
    ```

2.  **Replace hardcoded strings with translated versions**:
    -   **Before**:
        ```javascript
        Notifications.success('Success', 'User successfully created');
        ```
    -   **After**:
        ```javascript
        Notifications.success(
          this.$translate.instant('users.create-success-title', 'Success'), 
          this.$translate.instant('users.create-success-message', 'User successfully created')
        );
        ```

3.  **For error handling with promises**:
    -   **Before**:
        ```javascript
        catch (err) {
          Notifications.error('Failure', err, 'Unable to create user');
        }
        ```
    -   **After**:
        ```javascript
        catch (err) {
          Notifications.error(
            this.$translate.instant('failure.title', 'Failure'), 
            err, 
            this.$translate.instant('users.create-error', 'Unable to create user')
          );
        }
        ```

#### **For TypeScript Files (`.ts` files)**

Use translation keys instead of hardcoded strings for error messages passed to utility functions.

1.  **Replace hardcoded error messages with translation keys**:
    -   **Before**:
        ```typescript
        ...withGlobalError('Unable to retrieve containers')
        ```
    -   **After**:
        ```typescript
        ...withGlobalError('docker.containers.retrieve-error')
        ```

2.  **For parseAxiosError calls**:
    -   **Before**:
        ```typescript
        throw parseAxiosError(error, 'Unable to retrieve containers');
        ```
    -   **After**:
        ```typescript
        throw parseAxiosError(error, 'docker.containers.retrieve-error');
        ```

**Note**: TypeScript files don't require explicit imports for translation functionality since the translation keys are processed by the `i18n:sync` script and used at runtime by the i18n library that's already configured in the application.

### Step 2: Synchronize Translation Keys

After extracting the strings in the code, run the synchronization script. This script scans the codebase for the keys you've added and updates the `translation.json` files automatically.

-   **Run this command in both `package/server-ce` and `package/server-ee`:**
    ```bash
    yarn i18n:sync
    ```

### Step 3: Verify Your Changes

After completing a task (not subtask), run the following verification sequence. **All checks must pass before a task can be considered complete.**

1.  **Lint Check**: Run the linter to catch any style issues. This must be run for both CE and EE.
    ```bash
    # In package/server-ce/
    yarn lint

    # In package/server-ee/
    yarn lint
    ```

2.  **Type Check**: Run the TypeScript compiler to check for type errors. This must be run for both CE and EE.
    ```bash
    # In package/server-ce/
    npx tsc --noEmit

    # In package/server-ee/
    npx tsc --noEmit
    ```

3.  **Build Verification**: Run the build process to ensure the project is still buildable after changes. This must be run for both CE and EE.
    ```bash
    # In package/server-ce/
    yarn build

    # In package/server-ee/
    yarn build
    ```

4.  **Manual Verification**: Run the application and navigate to the views you have changed. Check that the new translations are displayed correctly and switch languages to verify.

---

## ✅ Detailed Migration Plan & Progress

Mark each item as done when the migration is complete for that section in **both CE and EE**.

### Task 1: Core Portainer Controllers

#### Task 1.1: Authentication Controller
- [x] `package/server-ce/app/portainer/views/auth/authController.js`
- [x] `package/server-ee/app/portainer/views/auth/authController.js`

#### Task 1.2: Account Controller
- [ ] `package/server-ce/app/portainer/views/account/accountController.js`
- [ ] `package/server-ee/app/portainer/views/account/accountController.js`

#### Task 1.3: Endpoint Controllers
- [ ] `package/server-ce/app/portainer/views/endpoints/access/endpointAccessController.js`
- [ ] `package/server-ee/app/portainer/views/endpoints/access/endpointAccessController.js`
- [ ] `package/server-ce/app/portainer/views/endpoints/edit/endpointController.js`
- [ ] `package/server-ee/app/portainer/views/endpoints/edit/endpointController.js`
- [ ] `package/server-ce/app/portainer/views/endpoints/kvm/endpointKVMController.js`
- [ ] `package/server-ee/app/portainer/views/endpoints/kvm/endpointKVMController.js`

#### Task 1.4: Group Controllers
- [ ] `package/server-ce/app/portainer/views/groups/access/groupAccessController.js`
- [ ] `package/server-ee/app/portainer/views/groups/access/groupAccessController.js`
- [ ] `package/server-ce/app/portainer/views/groups/create/createGroupController.js`
- [ ] `package/server-ee/app/portainer/views/groups/create/createGroupController.js`
- [ ] `package/server-ce/app/portainer/views/groups/edit/groupController.js`
- [ ] `package/server-ee/app/portainer/views/groups/edit/groupController.js`

#### Task 1.5: Initialization Controllers
- [ ] `package/server-ce/app/portainer/views/init/admin/initAdminController.js`
- [ ] `package/server-ee/app/portainer/views/init/admin/initAdminController.js`

#### Task 1.6: Logout Controller
- [ ] `package/server-ce/app/portainer/views/logout/logoutController.js`
- [ ] `package/server-ee/app/portainer/views/logout/logoutController.js`

#### Task 1.7: Main Controller
- [ ] `package/server-ce/app/portainer/views/main/mainController.js`
- [ ] `package/server-ee/app/portainer/views/main/mainController.js`

#### Task 1.8: Settings Controllers
- [ ] `package/server-ce/app/portainer/views/settings/authentication/settingsAuthenticationController.js`
- [ ] `package/server-ee/app/portainer/views/settings/authentication/settingsAuthenticationController.js`
- [ ] `package/server-ce/app/portainer/views/settings/edge-compute/settingsEdgeComputeController.js`
- [ ] `package/server-ee/app/portainer/views/settings/edge-compute/settingsEdgeComputeController.js`

#### Task 1.9: Stack Controllers
- [ ] `package/server-ce/app/portainer/views/stacks/create/createStackController.js`
- [ ] `package/server-ee/app/portainer/views/stacks/create/createStackController.js`
- [ ] `package/server-ce/app/portainer/views/stacks/edit/stackController.js`
- [ ] `package/server-ee/app/portainer/views/stacks/edit/stackController.js`
- [ ] `package/server-ce/app/portainer/views/stacks/stacksController.js`
- [ ] `package/server-ee/app/portainer/views/stacks/stacksController.js`

#### Task 1.10: Tags Controller
- [ ] `package/server-ce/app/portainer/views/tags/tagsController.js`
- [ ] `package/server-ee/app/portainer/views/tags/tagsController.js`

#### Task 1.11: User Controllers
- [ ] `package/server-ce/app/portainer/views/users/edit/userController.js`
- [ ] `package/server-ee/app/portainer/views/users/edit/userController.js`

#### Task 1.12: Internal Authentication Controller (EE Only)
- [ ] `package/server-ee/app/portainer/views/internal-auth/internal-auth.controller.js`

#### Task 1.13: License Initialization Controller (EE Only)
- [ ] `package/server-ee/app/portainer/views/init/init-license.view/init-license.view.controller.js`

### Task 2: Docker Controllers

#### Task 2.1: Config Controllers
- [ ] `package/server-ce/app/docker/views/configs/create/createConfigController.js`
- [ ] `package/server-ee/app/docker/views/configs/create/createConfigController.js`
- [ ] `package/server-ce/app/docker/views/configs/edit/configController.js`
- [ ] `package/server-ee/app/docker/views/configs/edit/configController.js`

#### Task 2.2: Container Controllers
- [ ] `package/server-ce/app/docker/views/containers/console/attachController.js`
- [ ] `package/server-ee/app/docker/views/containers/console/attachController.js`
- [ ] `package/server-ce/app/docker/views/containers/console/execController.js`
- [ ] `package/server-ee/app/docker/views/containers/console/execController.js`
- [ ] `package/server-ce/app/docker/views/containers/edit/containerController.js`
- [ ] `package/server-ee/app/docker/views/containers/edit/containerController.js`
- [ ] `package/server-ce/app/docker/views/containers/logs/containerLogsController.js`
- [ ] `package/server-ee/app/docker/views/containers/logs/containerLogsController.js`

#### Task 2.3: Image Controllers
- [ ] `package/server-ce/app/docker/views/images/build/buildImageController.js`
- [ ] `package/server-ee/app/docker/views/images/build/buildImageController.js`
- [ ] `package/server-ce/app/docker/views/images/edit/imageController.js`
- [ ] `package/server-ee/app/docker/views/images/edit/imageController.js`
- [ ] `package/server-ce/app/docker/views/images/import/importImageController.js`
- [ ] `package/server-ee/app/docker/views/images/import/importImageController.js`

#### Task 2.4: Network Controllers
- [ ] `package/server-ce/app/docker/views/networks/create/createNetworkController.js`
- [ ] `package/server-ee/app/docker/views/networks/create/createNetworkController.js`
- [ ] `package/server-ce/app/docker/views/networks/edit/networkController.js`
- [ ] `package/server-ee/app/docker/views/networks/edit/networkController.js`

#### Task 2.5: Secret Controllers
- [ ] `package/server-ce/app/docker/views/secrets/create/createSecretController.js`
- [ ] `package/server-ee/app/docker/views/secrets/create/createSecretController.js`
- [ ] `package/server-ce/app/docker/views/secrets/edit/secretController.js`
- [ ] `package/server-ee/app/docker/views/secrets/edit/secretController.js`

#### Task 2.6: Service Controllers
- [ ] `package/server-ce/app/docker/views/services/create/createserviceController.js`
- [ ] `package/server-ee/app/docker/views/services/create/createserviceController.js`
- [ ] `package/server-ce/app/docker/views/services/edit/serviceController.js`
- [ ] `package/server-ee/app/docker/views/services/edit/serviceController.js`

#### Task 2.7: Volume Controllers
- [ ] `package/server-ce/app/docker/views/volumes/create/createVolumeController.js`
- [ ] `package/server-ee/app/docker/views/volumes/create/createVolumeController.js`
- [ ] `package/server-ce/app/docker/views/volumes/edit/volumeController.js`
- [ ] `package/server-ee/app/docker/views/volumes/edit/volumeController.js`

#### Task 2.8: Host Controller
- [ ] `package/server-ce/app/docker/views/host/host-view-controller.js`
- [ ] `package/server-ee/app/docker/views/host/host-view-controller.js`

#### Task 2.9: Docker Features Configuration Controller
- [ ] `package/server-ce/app/docker/views/docker-features-configuration/docker-features-configuration-controller.js`
- [ ] `package/server-ee/app/docker/views/docker-features-configuration/docker-features-configuration-controller.js`

#### Task 2.10: Swarm Controller
- [ ] `package/server-ce/app/docker/views/swarm/swarmController.js`
- [ ] `package/server-ee/app/docker/views/swarm/swarmController.js`

### Task 3: Kubernetes Controllers

#### Task 3.1: Application Controllers
- [ ] `package/server-ce/app/kubernetes/views/applications/create/createApplicationController.js`
- [ ] `package/server-ee/app/kubernetes/views/applications/create/createApplicationController.js`
- [ ] `package/server-ce/app/kubernetes/views/applications/edit/applicationController.js`
- [ ] `package/server-ee/app/kubernetes/views/applications/edit/applicationController.js`

#### Task 3.2: Cluster Controllers
- [ ] `package/server-ce/app/kubernetes/views/cluster/clusterController.js`
- [ ] `package/server-ee/app/kubernetes/views/cluster/clusterController.js`
- [ ] `package/server-ce/app/kubernetes/views/cluster/node/nodeController.js`
- [ ] `package/server-ee/app/kubernetes/views/cluster/node/nodeController.js`

#### Task 3.3: Configuration Controllers
- [ ] `package/server-ce/app/kubernetes/views/configurations/create/createConfigurationController.js`
- [ ] `package/server-ee/app/kubernetes/views/configurations/create/createConfigurationController.js`
- [ ] `package/server-ce/app/kubernetes/views/configurations/edit/configurationController.js`
- [ ] `package/server-ee/app/kubernetes/views/configurations/edit/configurationController.js`

#### Task 3.4: Dashboard Controller
- [ ] `package/server-ce/app/kubernetes/views/dashboard/dashboardController.js`
- [ ] `package/server-ee/app/kubernetes/views/dashboard/dashboardController.js`

#### Task 3.5: Deploy Controller
- [ ] `package/server-ce/app/kubernetes/views/deploy/deployController.js`
- [ ] `package/server-ee/app/kubernetes/views/deploy/deployController.js`

#### Task 3.6: Namespace Controllers
- [ ] `package/server-ce/app/kubernetes/views/namespace/create/createNamespaceController.js`
- [ ] `package/server-ee/app/kubernetes/views/namespace/create/createNamespaceController.js`
- [ ] `package/server-ce/app/kubernetes/views/namespace/edit/namespaceController.js`
- [ ] `package/server-ee/app/kubernetes/views/namespace/edit/namespaceController.js`

#### Task 3.7: Resource Pool Controllers
- [ ] `package/server-ce/app/kubernetes/views/resource-pools/create/createResourcePoolController.js`
- [ ] `package/server-ee/app/kubernetes/views/resource-pools/create/createResourcePoolController.js`
- [ ] `package/server-ce/app/kubernetes/views/resource-pools/edit/resourcePoolController.js`
- [ ] `package/server-ee/app/kubernetes/views/resource-pools/edit/resourcePoolController.js`

#### Task 3.8: Summary Controller
- [ ] `package/server-ce/app/kubernetes/views/summary/summaryController.js`
- [ ] `package/server-ee/app/kubernetes/views/summary/summaryController.js`

#### Task 3.9: Volume Controllers
- [ ] `package/server-ce/app/kubernetes/views/volumes/create/createVolumeController.js`
- [ ] `package/server-ee/app/kubernetes/views/volumes/create/createVolumeController.js`
- [ ] `package/server-ce/app/kubernetes/views/volumes/edit/volumeController.js`
- [ ] `package/server-ee/app/kubernetes/views/volumes/edit/volumeController.js`

### Task 4: Business Edition (EE) Controllers

#### Task 4.1: Registry Controllers
- [ ] `package/server-ee/app/portainer/views/registries/create/createRegistryController.js`
- [ ] `package/server-ee/app/portainer/views/registries/edit/registryController.js`

#### Task 4.2: RBAC Controllers
- [ ] `package/server-ee/app/portainer/rbac/views/roles/rolesController.js`
- [ ] `package/server-ee/app/portainer/rbac/views/roles/edit/roleController.js`
- [ ] `package/server-ee/app/portainer/rbac/views/roles/create/createRoleController.js`
- [ ] `package/server-ee/app/portainer/rbac/views/teams/teamsController.js`
- [ ] `package/server-ee/app/portainer/rbac/views/teams/edit/teamController.js`
- [ ] `package/server-ee/app/portainer/rbac/views/teams/create/createTeamController.js`

### Task 5: TypeScript Query/Mutation Files

#### Task 5.1: Portainer User Queries
- [ ] `package/server-ce/app/react/portainer/users/queries/useCreateUserMutation.ts`
- [ ] `package/server-ee/app/react/portainer/users/queries/useCreateUserMutation.ts`
- [ ] `package/server-ce/app/react/portainer/users/queries/useDeleteUserMutation.ts`
- [ ] `package/server-ee/app/react/portainer/users/queries/useDeleteUserMutation.ts`

#### Task 5.2: Docker Container Queries
- [ ] `package/server-ce/app/react/docker/containers/queries/useContainers.ts`
- [ ] `package/server-ee/app/react/docker/containers/queries/useContainers.ts`
- [ ] `package/server-ce/app/react/docker/containers/queries/useContainer.ts`
- [ ] `package/server-ee/app/react/docker/containers/queries/useContainer.ts`
- [ ] `package/server-ce/app/react/docker/containers/queries/useContainerInspect.ts`
- [ ] `package/server-ee/app/react/docker/containers/queries/useContainerInspect.ts`
- [ ] `package/server-ce/app/react/docker/containers/queries/useContainerStats.ts`
- [ ] `package/server-ee/app/react/docker/containers/queries/useContainerStats.ts`
- [ ] `package/server-ce/app/react/docker/containers/queries/useContainerTop.ts`
- [ ] `package/server-ee/app/react/docker/containers/queries/useContainerTop.ts`
- [ ] `package/server-ce/app/react/docker/containers/queries/useCreateExecMutation.ts`
- [ ] `package/server-ee/app/react/docker/containers/queries/useCreateExecMutation.ts`
- [ ] `package/server-ce/app/react/docker/containers/queries/useUpdateContainer.ts`
- [ ] `package/server-ee/app/react/docker/containers/queries/useUpdateContainer.ts`

#### Task 5.3: Docker Image Queries
- [ ] `package/server-ce/app/react/docker/images/queries/useImages.ts`
- [ ] `package/server-ee/app/react/docker/images/queries/useImages.ts`
- [ ] `package/server-ce/app/react/docker/images/queries/useBuildImageMutation.ts`
- [ ] `package/server-ee/app/react/docker/images/queries/useBuildImageMutation.ts`
- [ ] `package/server-ce/app/react/docker/images/queries/usePullImageMutation.ts`
- [ ] `package/server-ee/app/react/docker/images/queries/usePullImageMutation.ts`
- [ ] `package/server-ce/app/react/docker/images/queries/usePushImageMutation.ts`
- [ ] `package/server-ee/app/react/docker/images/queries/usePushImageMutation.ts`

#### Task 5.4: Docker Volume Queries
- [ ] `package/server-ce/app/react/docker/volumes/queries/useVolumes.ts`
- [ ] `package/server-ee/app/react/docker/volumes/queries/useVolumes.ts`
- [ ] `package/server-ce/app/react/docker/volumes/queries/useVolume.ts`
- [ ] `package/server-ee/app/react/docker/volumes/queries/useVolume.ts`
- [ ] `package/server-ce/app/react/docker/volumes/queries/useCreateVolume.ts`
- [ ] `package/server-ee/app/react/docker/volumes/queries/useCreateVolume.ts`
- [ ] `package/server-ce/app/react/docker/volumes/queries/useCreateVolumeMutation.ts`
- [ ] `package/server-ee/app/react/docker/volumes/queries/useCreateVolumeMutation.ts`
- [ ] `package/server-ce/app/react/docker/volumes/queries/useRemoveVolumeMutation.ts`
- [ ] `package/server-ee/app/react/docker/volumes/queries/useRemoveVolumeMutation.ts`

#### Task 5.5: Docker Network Queries
- [ ] `package/server-ce/app/react/docker/networks/queries/useNetworks.ts`
- [ ] `package/server-ee/app/react/docker/networks/queries/useNetworks.ts`
- [ ] `package/server-ce/app/react/docker/networks/queries/useNetwork.ts`
- [ ] `package/server-ee/app/react/docker/networks/queries/useNetwork.ts`
- [ ] `package/server-ce/app/react/docker/networks/queries/useCreateNetworkMutation.ts`
- [ ] `package/server-ee/app/react/docker/networks/queries/useCreateNetworkMutation.ts`
- [ ] `package/server-ce/app/react/docker/networks/queries/useDeleteNetworkMutation.ts`
- [ ] `package/server-ee/app/react/docker/networks/queries/useDeleteNetworkMutation.ts`
- [ ] `package/server-ce/app/react/docker/networks/queries/useConnectContainerMutation.ts`
- [ ] `package/server-ee/app/react/docker/networks/queries/useConnectContainerMutation.ts`
- [ ] `package/server-ce/app/react/docker/networks/queries/useDisconnectContainerMutation.ts`
- [ ] `package/server-ee/app/react/docker/networks/queries/useDisconnectContainerMutation.ts`

#### Task 5.6: Docker Service Queries
- [ ] `package/server-ce/app/react/docker/services/queries/useServices.ts`
- [ ] `package/server-ee/app/react/docker/services/queries/useServices.ts`
- [ ] `package/server-ce/app/react/docker/services/queries/useService.ts`
- [ ] `package/server-ee/app/react/docker/services/queries/useService.ts`
- [ ] `package/server-ce/app/react/docker/services/queries/useCreateServiceMutation.ts`
- [ ] `package/server-ee/app/react/docker/services/queries/useCreateServiceMutation.ts`
- [ ] `package/server-ce/app/react/docker/services/queries/useUpdateServiceMutation.ts`
- [ ] `package/server-ee/app/react/docker/services/queries/useUpdateServiceMutation.ts`

#### Task 5.7: Docker Config/Secret Queries
- [ ] `package/server-ce/app/react/docker/configs/queries/useConfigs.ts`
- [ ] `package/server-ee/app/react/docker/configs/queries/useConfigs.ts`
- [ ] `package/server-ce/app/react/docker/configs/queries/useConfig.ts`
- [ ] `package/server-ee/app/react/docker/configs/queries/useConfig.ts`
- [ ] `package/server-ce/app/react/docker/configs/queries/useCreateConfigMutation.ts`
- [ ] `package/server-ee/app/react/docker/configs/queries/useCreateConfigMutation.ts`
- [ ] `package/server-ce/app/react/docker/configs/queries/useDeleteConfigMutation.ts`
- [ ] `package/server-ee/app/react/docker/configs/queries/useDeleteConfigMutation.ts`

#### Task 5.8: Docker Swarm Queries
- [ ] `package/server-ce/app/react/docker/swarm/queries/useSwarm.ts`
- [ ] `package/server-ee/app/react/docker/swarm/queries/useSwarm.ts`

#### Task 5.9: Kubernetes Queries
- [ ] `package/server-ce/app/react/kubernetes/queries/useEvents.ts`
- [ ] `package/server-ee/app/react/kubernetes/queries/useEvents.ts`
- [ ] `package/server-ce/app/react/kubernetes/applications/queries/useApplications.ts`
- [ ] `package/server-ee/app/react/kubernetes/applications/queries/useApplications.ts`
- [ ] `package/server-ce/app/react/kubernetes/applications/queries/useApplication.ts`
- [ ] `package/server-ee/app/react/kubernetes/applications/queries/useApplication.ts`
- [ ] `package/server-ce/app/react/kubernetes/namespaces/queries/useNamespacesQuery.ts`
- [ ] `package/server-ee/app/react/kubernetes/namespaces/queries/useNamespacesQuery.ts`
- [ ] `package/server-ce/app/react/kubernetes/namespaces/queries/useNamespaceQuery.ts`
- [ ] `package/server-ee/app/react/kubernetes/namespaces/queries/useNamespaceQuery.ts`
- [ ] `package/server-ce/app/react/kubernetes/volumes/queries/useVolumesQuery.ts`
- [ ] `package/server-ee/app/react/kubernetes/volumes/queries/useVolumesQuery.ts`

#### Task 5.10: Portainer Environment Queries
- [ ] `package/server-ce/app/react/portainer/environments/queries/useEnvironmentList.ts`
- [ ] `package/server-ee/app/react/portainer/environments/queries/useEnvironmentList.ts`
- [ ] `package/server-ce/app/react/portainer/environments/queries/useEnvironment.ts`
- [ ] `package/server-ee/app/react/portainer/environments/queries/useEnvironment.ts`
- [ ] `package/server-ce/app/react/portainer/environments/queries/useCreateEnvironmentMutation.ts`
- [ ] `package/server-ee/app/react/portainer/environments/queries/useCreateEnvironmentMutation.ts`
- [ ] `package/server-ce/app/react/portainer/environments/queries/useDeleteEnvironmentsMutation.ts`
- [ ] `package/server-ee/app/react/portainer/environments/queries/useDeleteEnvironmentsMutation.ts`

#### Task 5.11: Portainer Registry Queries
- [ ] `package/server-ce/app/react/portainer/registries/queries/useRegistries.ts`
- [ ] `package/server-ee/app/react/portainer/registries/queries/useRegistries.ts`
- [ ] `package/server-ce/app/react/portainer/registries/queries/useRegistry.ts`
- [ ] `package/server-ee/app/react/portainer/registries/queries/useRegistry.ts`

#### Task 5.12: Portainer Team Queries
- [ ] `package/server-ce/app/react/portainer/users/teams/queries/useTeams.ts`
- [ ] `package/server-ee/app/react/portainer/users/teams/queries/useTeams.ts`
- [ ] `package/server-ce/app/react/portainer/users/teams/queries/useTeam.ts`
- [ ] `package/server-ee/app/react/portainer/users/teams/queries/useTeam.ts`

### Task 6: TypeScript Modal/Utility Files

#### Task 6.1: Container Modals
- [ ] `package/server-ce/app/react/docker/containers/common/confirm-container-delete-modal.ts`
- [ ] `package/server-ee/app/react/docker/containers/common/confirm-container-delete-modal.ts`

#### Task 6.2: Service Modals
- [ ] `package/server-ce/app/react/docker/services/common/update-service-modal.ts`
- [ ] `package/server-ee/app/react/docker/services/common/update-service-modal.ts`

*This plan is a living document. Please update it as tasks are completed.*