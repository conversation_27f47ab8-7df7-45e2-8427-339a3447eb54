# Portainer i18n Migration Plan & Guide

This document provides a complete guide and plan for migrating the Portainer CE and EE codebases to full i18n support, building upon the existing infrastructure.

---

## ⚠️ CRITICAL IMPLEMENTATION RULES ⚠️

These rules are **mandatory** for all developers to follow to ensure a smooth and consistent i18n migration.

### 🔴 Rule 1: Work on Both Codebases Simultaneously
**ALWAYS work on BOTH CE and EE versions simultaneously for every change.**

-   **Paths**: `package/server-ce/` (CE) and `package/server-ee/` (EE)
-   **Why**: EE duplicates most CE components but can have overrides. Both codebases MUST be kept in sync for all i18n changes.
-   **Process**: For every file migrated in CE, check if it exists in EE and migrate it at the same time. Both files should be part of the same commit.

### 🔴 Rule 2: Manage Translation Files Properly
**NEVER directly edit `translation.json` files.**

-   **Why**: Translation files are managed by an automated script (`yarn i18n:sync`) that scans the code for keys. Manual edits will be overwritten.
-   **Process**: Only add translation keys within the code itself using the correct syntax for React or AngularJS. The automation will handle the rest.

### 🔴 Rule 3: Branch Management
**ALL work must be done on the `feat/i18n-foundation` branch.**

-   **Why**: Isolates i18n migration work from other development and ensures proper coordination.
-   **Process**: 
    1. Work exclusively on the `feat/i18n-foundation` branch for all i18n tasks
    2. ALWAYS fetch latest changes from `develop` branch and rebase `feat/i18n-foundation` before any modification:
        ```bash
        git fetch origin
        git rebase origin/develop
        ```
    3. WHEN conflicts occur during rebase, ALWAYS prioritize keeping our i18n implementation and avoid modifying any incoming changes:
        - Prioritize our i18n changes
        - Do NOT modify any incoming changes unrelated to i18n
        - After resolving conflicts, run type and lint checks again to ensure everything is working
        - Build both CE and EE projects to verify nothing is broken before continuing work

### 🔴 Rule 4: Verification Process
**Follow the proper verification sequence for quality assurance.**

-   **Why**: Ensures code quality while improving efficiency by reducing redundant verification runs.
-   **Process**: 
    1. DO NOT run unit tests for task or subtask verification (they can fail for unrelated reasons)
    2. ONLY run type check and lint check when a whole task is finished, not for each subtask
    3. After completing all subtasks for a task, run in sequence:
        - Lint checks for both CE and EE
        - Type checks for both CE and EE
        - Build verification for both CE and EE:
            ```bash
            # In package/server-ce/
            yarn build
            
            # In package/server-ee/
            yarn build
            ```
    4. DO NOT commit changes immediately when a task or subtask is finished - keep changes local until a logical group of work is complete and ready for review

### 🔴 Rule 5: Documentation and Tracking
**Maintain proper documentation and progress tracking.**

-   **Why**: Provides clear visibility of completed work and progress tracking.
-   **Process**: 
    1. ALWAYS mark tasks as done in the progress section when finished
    2. Update the task checklist in the "Detailed Migration Plan & Progress" section to reflect completed work
    3. Follow commit message pattern:
        ```bash
        feat(i18n): Task X - [Brief description]
        - Migrate [component names] with [number] translation keys
        - Add comprehensive [feature area] translation sections
        - [Other relevant details]
        ```

### 🔴 Rule 6: No Direct Commits
**DO NOT commit any changes directly to the repository during the migration process.**

-   **Why**: Ensures proper review and coordination of all changes before they are permanently added to the codebase.
-   **Process**: 
    1. Keep all changes local until instructed to commit
    2. All changes will be committed in bulk at the end of the migration process
    3. Follow the established commit message pattern when committing

### 🔴 Rule 7: Dynamic Task List Updates
**Update the task list dynamically when discovering new files or folders requiring i18n support.**

-   **Why**: Ensures comprehensive i18n coverage without missing any files or folders in server-ce and server-ee.
-   **Process**: 
    1. When working on a task, if you discover additional files or folders in the same location or module that require i18n support, add them to the current task list
    2. Continue working on the modifications for these newly discovered items as part of the current task
    3. This plan is a living document that should be updated as new items are found to ensure complete i18n implementation

---

## 📖 Development Guide & Workflow

This guide explains how to implement internationalization in Portainer's hybrid React/AngularJS codebase.

### Step 1: Identify and Extract Hardcoded Strings

Locate all user-facing strings in the target files and replace them using the appropriate method for the framework.

#### **For React Code (`.tsx`, `.jsx`)**

Use the `useTranslation` hook from `react-i18next`.

1.  **Import the hook**:
    ```javascript
    import { useTranslation } from 'react-i18next';
    ```
2.  **Instantiate the hook** inside your component:
    ```javascript
    const { t } = useTranslation();
    ```
3.  **Replace the string** with a call to the `t` function, providing a unique key and the original string as a fallback.

    -   **Before**:
        ```jsx
        <button>Save Changes</button>
        ```
    -   **After**:
        ```jsx
        <button>{t('common.actions.save', 'Save Changes')}</button>
        ```

#### **For AngularJS Code (`.html`)**

Use the `translate` filter.

1.  Replace the hardcoded string with the `translate` filter syntax, providing a unique key and the original string as a fallback.

    -   **Before**:
        ```html
        <label>Username</label>
        ```
    -   **After**:
        ```html
        <label>{{ 'auth.username' | translate:'Username' }}</label>
        ```

### Step 2: Synchronize Translation Keys

After extracting the strings in the code, run the synchronization script. This script scans the codebase for the keys you've added and updates the `translation.json` files automatically.

-   **Run this command in both `package/server-ce` and `package/server-ee`:**
    ```bash
    yarn i18n:sync
    ```

### Step 3: Verify Your Changes

After completing a task (not subtask), run the following verification sequence. **All checks must pass before a task can be considered complete.**

1.  **Lint Check**: Run the linter to catch any style issues. This must be run for both CE and EE.
    ```bash
    # In package/server-ce/
    yarn lint

    # In package/server-ee/
    yarn lint
    ```

2.  **Type Check**: Run the TypeScript compiler to check for type errors. This must be run for both CE and EE.
    ```bash
    # In package/server-ce/
    npx tsc --noEmit

    # In package/server-ee/
    npx tsc --noEmit
    ```

3.  **Build Verification**: Run the build process to ensure the project is still buildable after changes. This must be run for both CE and EE.
    ```bash
    # In package/server-ce/
    yarn build

    # In package/server-ee/
    yarn build
    ```

4.  **Manual Verification**: Run the application and navigate to the views you have changed. Check that the new translations are displayed correctly and switch languages to verify.

---

## ✅ Detailed Migration Plan & Progress

Mark each item as done when the migration is complete for that section in **both CE and EE**.

### Task 1: Core Portainer

#### Task 1.1: Authentication
- [x] `package/server-ce/app/portainer/views/auth/auth.html`
- [x] `package/server-ee/app/portainer/views/auth/auth.html`
- [x] `package/server-ce/app/portainer/views/auth/authController.js`
- [x] `package/server-ee/app/portainer/views/auth/authController.js`

#### Task 1.2: User Management
- [x] `package/server-ce/app/portainer/views/users/edit/user.html`
- [x] `package/server-ee/app/portainer/views/users/edit/user.html`
- [x] `package/server-ce/app/react/portainer/users/ListView/NewUserForm/NewUserForm.tsx`
- [x] `package/server-ee/app/react/portainer/users/ListView/NewUserForm/NewUserForm.tsx`
- [x] `package/server-ce/app/react/portainer/users/ListView/UsersDatatable/UsersDatatable.tsx`
- [x] `package/server-ee/app/react/portainer/users/ListView/UsersDatatable/UsersDatatable.tsx`

#### Task 1.3: Account & Profile
- [x] `package/server-ce/app/portainer/views/account/account.html`
- [x] `package/server-ee/app/portainer/views/account/account.html`
- [x] `package/server-ce/app/react/portainer/account/AccountView/LanguageSettings/LanguageSettingsWidget.tsx`
- [x] `package/server-ee/app/react/portainer/account/AccountView/LanguageSettings/LanguageSettingsWidget.tsx`
- [x] `package/server-ce/app/react/portainer/account/CreateAccessTokenView/CreateUserAccessTokenInnerForm.tsx`
- [x] `package/server-ee/app/react/portainer/account/CreateAccessTokenView/CreateUserAccessTokenInnerForm.tsx`

#### Task 1.4: Dashboard & Home
- [x] `package/server-ce/app/react/portainer/HomeView/HomeView.tsx`
- [x] `package/server-ee/app/react/portainer/HomeView/HomeView.tsx`
- [x] `package/server-ce/app/react/portainer/HomeView/EnvironmentList/EnvironmentList.tsx`
- [x] `package/server-ee/app/react/portainer/HomeView/EnvironmentList/EnvironmentList.tsx`
- [x] `package/server-ce/app/react/portainer/HomeView/EnvironmentList/NoEnvironmentsInfoPanel.tsx`
- [x] `package/server-ee/app/react/portainer/HomeView/EnvironmentList/NoEnvironmentsInfoPanel.tsx`
- [x] `package/server-ce/app/react/portainer/HomeView/EnvironmentList/UpdateBadge.tsx`
- [x] `package/server-ee/app/react/portainer/HomeView/EnvironmentList/UpdateBadge.tsx`

#### Task 1.5: Endpoints (Environments)
- [x] `package/server-ce/app/portainer/views/endpoints/edit/endpoint.html`
- [x] `package/server-ce/app/react/portainer/environments/ListView/EnvironmentsDatatable.tsx`
- [x] `package/server-ee/app/react/portainer/environments/ListView/EnvironmentsDatatable.tsx`

#### Task 1.6: Settings
- [x] `package/server-ce/app/react/portainer/settings/SettingsView/SettingsView.tsx`
- [x] `package/server-ee/app/react/portainer/settings/SettingsView/SettingsView.tsx`

#### Task 1.7: Groups & Tags
- [x] `package/server-ce/app/portainer/views/tags/tags.html`
- [x] `package/server-ee/app/portainer/views/tags/tags.html`

#### Task 1.8: Initialization & Logout
- [x] `package/server-ce/app/portainer/views/logout/logout.html`
- [x] `package/server-ee/app/portainer/views/logout/logout.html`

#### Task 1.9: Internal Authentication (EE Only)
- [x] `package/server-ee/app/portainer/views/internal-auth/internal-auth.html`

#### Task 1.10: Additional Portainer Views
- [x] `package/server-ce/app/portainer/views/endpoints/access/endpointAccess.html`
- [x] `package/server-ee/app/portainer/views/endpoints/access/endpointAccess.html`
- [x] `package/server-ce/app/portainer/views/endpoints/kvm/endpointKVM.html`
- [x] `package/server-ee/app/portainer/views/endpoints/kvm/endpointKVM.html`
- [x] `package/server-ce/app/portainer/views/groups/access/groupAccess.html`
- [x] `package/server-ee/app/portainer/views/groups/access/groupAccess.html`
- [x] `package/server-ce/app/portainer/views/groups/create/creategroup.html`
- [x] `package/server-ee/app/portainer/views/groups/create/creategroup.html`
- [x] `package/server-ce/app/portainer/views/groups/edit/group.html`
- [x] `package/server-ee/app/portainer/views/groups/edit/group.html`
- [x] `package/server-ce/app/portainer/views/init/admin/initAdmin.html`
- [x] `package/server-ee/app/portainer/views/init/admin/initAdmin.html`
- [x] `package/server-ce/app/portainer/views/settings/authentication/settingsAuthentication.html`
- [x] `package/server-ee/app/portainer/views/settings/authentication/settingsAuthentication.html`
- [x] `package/server-ce/app/portainer/views/settings/edge-compute/settingsEdgeCompute.html`
- [x] `package/server-ee/app/portainer/views/settings/edge-compute/settingsEdgeCompute.html`
- [x] `package/server-ce/app/portainer/views/stacks/edit/stack.html`
- [x] `package/server-ee/app/portainer/views/stacks/edit/stack.html`
- [x] `package/server-ce/app/portainer/views/stacks/stacks.html`
- [x] `package/server-ee/app/portainer/views/stacks/stacks.html`


#### Task 1.11: Portainer EE-Only Views
- [x] `package/server-ee/app/portainer/views/init/init-license.view/init-license.view.html`

#### Task 1.12: Portainer Datatables
- [x] `package/server-ce/app/react/portainer/environments/environment-groups/ListView/EnvironmentGroupsDatatable/EnvironmentGroupsDatatable.tsx`
- [x] `package/server-ee/app/react/portainer/environments/environment-groups/ListView/EnvironmentGroupsDatatable/EnvironmentGroupsDatatable.tsx`
- [x] `package/server-ce/app/react/portainer/registries/ListView/RegistriesDatatable/RegistriesDatatable.tsx`
- [x] `package/server-ee/app/react/portainer/registries/ListView/RegistriesDatatable/RegistriesDatatable.tsx`
- [x] `package/server-ce/app/react/portainer/users/teams/ListView/TeamsDatatable/TeamsDatatable.tsx`
- [x] `package/server-ee/app/react/portainer/users/teams/ListView/TeamsDatatable/TeamsDatatable.tsx`

#### Task 1.13: Portainer EE-Only Datatables
- [x] `package/server-ee/app/react/portainer/environments/update-schedules/ListView/UpdateSchedulesDatatable/UpdateSchedulesDatatable.tsx`
- [x] `package/server-ee/app/react/portainer/licenses/ListView/LicensesDatatable/LicensesDatatable.tsx`

#### Task 1.14: Portainer Account Components
- [x] `package/server-ce/app/react/portainer/account/AccountView/ApplicationSettings/ApplicationSettingsWidget.tsx`
- [x] `package/server-ee/app/react/portainer/account/AccountView/ApplicationSettings/ApplicationSettingsWidget.tsx`

#### Task 1.15: Portainer Settings Components
- [x] `package/server-ce/app/react/portainer/settings/SettingsView/ApplicationSettingsPanel/ApplicationSettingsPanel.tsx`
- [x] `package/server-ee/app/react/portainer/settings/SettingsView/ApplicationSettingsPanel/ApplicationSettingsPanel.tsx`

#### Task 1.16: Portainer Environment Components
- [x] `package/server-ce/app/react/portainer/environments/TagsView/TagsDatatable.tsx`
- [x] `package/server-ee/app/react/portainer/environments/TagsView/TagsDatatable.tsx`

#### Task 1.17: Portainer Home View Components
- [x] `package/server-ce/app/react/portainer/HomeView/EnvironmentList/EnvironmentItem/EnvironmentItem.tsx`
- [x] `package/server-ee/app/react/portainer/HomeView/EnvironmentList/EnvironmentItem/EnvironmentItem.tsx`

### Task 1.18: Additional Portainer Views (Newly Discovered)
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/quick-actions.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/quick-actions.tsx`

### Task 2: Docker

#### Task 2.1: Containers
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/ContainersDatatable.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/ContainersDatatable.tsx`
- [x] `package/server-ce/app/react/docker/containers/CreateView/CreateView.tsx`
- [x] `package/server-ee/app/react/docker/containers/CreateView/CreateView.tsx`
- [x] `package/server-ce/app/react/docker/containers/components/ContainerQuickActions/ContainerQuickActions.tsx`
- [x] `package/server-ee/app/react/docker/containers/components/ContainerQuickActions/ContainerQuickActions.tsx`
- [x] `package/server-ce/app/react/docker/containers/common/confirm-container-delete-modal.ts`
- [x] `package/server-ee/app/react/docker/containers/common/confirm-container-delete-modal.ts`
- [x] `package/server-ce/app/react/docker/containers/components/NetworkSelector.tsx`
- [x] `package/server-ee/app/react/docker/containers/components/NetworkSelector.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/ContainersDatatableActions.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/ContainersDatatableActions.tsx`
- [x] `package/server-ee/app/react/edge/edge-devices/ContainersView/ContainersDatatableActions.tsx`

#### Task 2.2: Images
- [x] `package/server-ce/app/docker/views/images/build/buildimage.html`
- [x] `package/server-ee/app/docker/views/images/build/buildimage.html`
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/ImagesDatatable.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/ImagesDatatable.tsx`
- [x] `package/server-ce/app/react/docker/images/common/ConfirmExportModal.tsx`
- [x] `package/server-ee/app/react/docker/images/common/ConfirmExportModal.tsx`
- [x] `package/server-ce/app/react/docker/images/ItemView/DockerfileDetails.tsx`
- [x] `package/server-ee/app/react/docker/images/ItemView/DockerfileDetails.tsx`
- [x] `package/server-ce/app/react/docker/images/ItemView/RegistrySelectPrompt.tsx`
- [x] `package/server-ee/app/react/docker/images/ItemView/RegistrySelectPrompt.tsx`
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/columns/created.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/columns/created.tsx`
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/columns/host.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/columns/host.tsx`
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/columns/id.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/columns/id.tsx`
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/columns/size.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/columns/size.tsx`
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/columns/tags.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/columns/tags.tsx`

#### Task 2.3: Volumes
- [x] `package/server-ce/app/docker/views/volumes/create/createvolume.html`
- [x] `package/server-ee/app/docker/views/volumes/create/createvolume.html`
- [x] `package/server-ce/app/react/docker/volumes/ListView/VolumesDatatable/VolumesDatatable.tsx`
- [x] `package/server-ee/app/react/docker/volumes/ListView/VolumesDatatable/VolumesDatatable.tsx`
- [x] `package/server-ce/app/react/docker/volumes/ListView/VolumesDatatable/columns/index.ts`
- [x] `package/server-ee/app/react/docker/volumes/ListView/VolumesDatatable/columns/index.ts`
- [x] `package/server-ce/app/react/docker/volumes/ListView/VolumesDatatable/columns/name.tsx`
- [x] `package/server-ee/app/react/docker/volumes/ListView/VolumesDatatable/columns/name.tsx`
- [x] `package/server-ce/app/docker/views/volumes/volumes.html`
- [x] `package/server-ee/app/docker/views/volumes/volumes.html`
- [x] `package/server-ce/app/docker/views/volumes/browse/browsevolume.html`
- [x] `package/server-ee/app/docker/views/volumes/browse/browsevolume.html`
- [x] `package/server-ce/app/docker/views/volumes/edit/volume.html`
- [x] `package/server-ee/app/docker/views/volumes/edit/volume.html`
- [x] `package/server-ce/app/react/docker/volumes/ListView/VolumesDatatable/columns/created.tsx`
- [x] `package/server-ee/app/react/docker/volumes/ListView/VolumesDatatable/columns/created.tsx`
- [x] `package/server-ce/app/react/docker/volumes/ListView/VolumesDatatable/columns/driver.tsx`
- [x] `package/server-ee/app/react/docker/volumes/ListView/VolumesDatatable/columns/driver.tsx`
- [x] `package/server-ce/app/react/docker/volumes/ListView/VolumesDatatable/columns/mountpoint.tsx`
- [x] `package/server-ee/app/react/docker/volumes/ListView/VolumesDatatable/columns/mountpoint.tsx`
- [x] `package/server-ce/app/react/docker/volumes/ListView/VolumesDatatable/columns/stackName.tsx`
- [x] `package/server-ee/app/react/docker/volumes/ListView/VolumesDatatable/columns/stackName.tsx`
- [x] `package/server-ce/app/react/docker/volumes/BrowseView/AgentVolumeBrowser.tsx`
- [x] `package/server-ee/app/react/docker/volumes/BrowseView/AgentVolumeBrowser.tsx`

#### Task 2.4: Networks
- [x] `package/server-ce/app/react/docker/networks/ItemView/NetworkQuickActions.tsx`
- [x] `package/server-ee/app/react/docker/networks/ItemView/NetworkQuickActions.tsx`
- [x] `package/server-ce/app/react/docker/networks/ItemView/index.ts`
- [x] `package/server-ee/app/react/docker/networks/ItemView/index.ts`

#### Task 2.5: Services
- [x] `package/server-ce/app/react/docker/services/ItemView/ServiceQuickActions.tsx`
- [x] `package/server-ee/app/react/docker/services/ItemView/ServiceQuickActions.tsx`
- [x] `package/server-ce/app/react/docker/services/ItemView/index.ts`
- [x] `package/server-ee/app/react/docker/services/ItemView/index.ts`

#### Task 2.6: Stacks
- [x] `package/server-ce/app/portainer/views/stacks/create/createstack.html`
- [x] `package/server-ce/app/portainer/views/stacks/stacks.html`
- [x] `package/server-ce/app/portainer/views/stacks/edit/stack.html`
- [x] `package/server-ee/app/portainer/views/stacks/create/createstack.html`
- [x] `package/server-ee/app/portainer/views/stacks/stacks.html`
- [x] `package/server-ee/app/portainer/views/stacks/edit/stack.html`
- [x] `package/server-ce/app/react/docker/stacks/ListView/StacksDatatable/StacksDatatable.tsx`
- [x] `package/server-ee/app/react/docker/stacks/ListView/StacksDatatable/StacksDatatable.tsx`
- [x] `package/server-ce/app/react/docker/stacks/ItemView/StackQuickActions.tsx`
- [x] `package/server-ee/app/react/docker/stacks/ItemView/StackQuickActions.tsx`
- [x] `package/server-ce/app/react/docker/stacks/ItemView/index.ts`
- [x] `package/server-ee/app/react/docker/stacks/ItemView/index.ts`

#### Task 2.7: Additional Docker Components
- [x] `package/server-ce/app/docker/views/configs/configs.html`
- [x] `package/server-ee/app/docker/views/configs/configs.html`
- [x] `package/server-ce/app/docker/views/docker-features-configuration/docker-features-configuration.html`
- [x] `package/server-ee/app/docker/views/docker-features-configuration/docker-features-configuration.html`
- [x] `package/server-ce/app/docker/views/secrets/secrets.html`
- [x] `package/server-ee/app/docker/views/secrets/secrets.html`
- [x] `package/server-ce/app/docker/views/swarm/swarm.html`
- [x] `package/server-ee/app/docker/views/swarm/swarm.html`

#### Task 2.8: Docker Dashboard and Events
- [x] `package/server-ce/app/react/docker/DashboardView/DashboardView.tsx`
- [x] `package/server-ee/app/react/docker/DashboardView/DashboardView.tsx`
- [x] `package/server-ce/app/react/docker/DashboardView/ContainerStatus.tsx`
- [x] `package/server-ee/app/react/docker/DashboardView/ContainerStatus.tsx`
- [x] `package/server-ce/app/react/docker/DashboardView/EnvironmentInfo.tsx`
- [x] `package/server-ee/app/react/docker/DashboardView/EnvironmentInfo.tsx`
- [x] `package/server-ce/app/react/docker/events/ListView.tsx`
- [x] `package/server-ee/app/react/docker/events/ListView.tsx`
- [x] `package/server-ce/app/react/docker/events/EventsDatatables.tsx`
- [x] `package/server-ee/app/react/docker/events/EventsDatatables.tsx`

#### Task 2.9: Docker Networks (Additional Components)
- [x] `package/server-ce/app/react/docker/networks/ItemView/ItemView.tsx`
- [x] `package/server-ce/app/react/docker/networks/ListView/columns/name.tsx`
- [x] `package/server-ee/app/react/docker/networks/ItemView/ItemView.tsx`
- [x] `package/server-ee/app/react/docker/networks/ListView/columns/name.tsx`

#### Task 2.10: Docker Services (Additional Components)
- [x] `package/server-ce/app/react/docker/services/ItemView/ServiceWidget.tsx`
- [x] `package/server-ee/app/react/docker/services/ItemView/ServiceWidget.tsx`

#### Task 2.11: Docker Configs and Secrets
- [x] `package/server-ce/app/react/docker/configs/ListView/ConfigsDatatable/ConfigsDatatable.tsx`
- [x] `package/server-ee/app/react/docker/configs/ListView/ConfigsDatatable/ConfigsDatatable.tsx`
- [x] `package/server-ce/app/react/docker/secrets/ListView/SecretsDatatable.tsx`
- [x] `package/server-ee/app/react/docker/secrets/ListView/SecretsDatatable.tsx`

#### Task 2.12: Docker Swarm
- [x] `package/server-ce/app/react/docker/swarm/SwarmView/NodesDatatable/NodesDatatable.tsx`
- [x] `package/server-ee/app/react/docker/swarm/SwarmView/NodesDatatable/NodesDatatable.tsx`

#### Task 2.13: Docker Images (Additional Components)
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/columns/created.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/columns/created.tsx`
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/columns/host.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/columns/host.tsx`
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/columns/id.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/columns/id.tsx`
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/columns/size.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/columns/size.tsx`
- [x] `package/server-ce/app/react/docker/images/ListView/ImagesDatatable/columns/tags.tsx`
- [x] `package/server-ee/app/react/docker/images/ListView/ImagesDatatable/columns/tags.tsx`

#### Task 2.14: Docker Containers (Additional Components)
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/created.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/created.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/gpus.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/gpus.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/host.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/host.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/image.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/image.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/index.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/index.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/ip.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/ip.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/name.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/name.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/ports.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/ports.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/quick-actions.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/quick-actions.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/stack.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/stack.tsx`
- [x] `package/server-ce/app/react/docker/containers/ListView/ContainersDatatable/columns/state.tsx`
- [x] `package/server-ee/app/react/docker/containers/ListView/ContainersDatatable/columns/state.tsx`

#### Task 2.15: Docker Component Utilities
- [x] `package/server-ce/app/react/docker/components/ImageStatus/ImageStatus.tsx`
- [x] `package/server-ee/app/react/docker/components/ImageStatus/ImageStatus.tsx`
- [x] `package/server-ce/app/react/docker/components/ImageStatus/PublishedPortLink.tsx`
- [x] `package/server-ee/app/react/docker/components/ImageStatus/PublishedPortLink.tsx`

#### Task 2.16: Docker Stacks (Additional Columns)
- [x] `package/server-ce/app/react/docker/stacks/ListView/StacksDatatable/columns/name.tsx`
- [x] `package/server-ee/app/react/docker/stacks/ListView/StacksDatatable/columns/name.tsx`

#### Task 2.17: Additional Docker Views
- [x] `package/server-ce/app/docker/views/containers/edit/container.html`
- [x] `package/server-ee/app/docker/views/containers/edit/container.html`
- [x] `package/server-ce/app/docker/views/containers/logs/containerlogs.html`
- [x] `package/server-ce/app/docker/views/images/edit/image.html`
- [x] `package/server-ee/app/docker/views/images/edit/image.html`
- [x] `package/server-ce/app/docker/views/images/import/importimage.html`
- [x] `package/server-ee/app/docker/views/images/import/importimage.html`
- [x] `package/server-ce/app/docker/views/networks/networks.html`
- [x] `package/server-ee/app/docker/views/networks/networks.html`
- [x] `package/server-ce/app/docker/views/services/services.html`
- [x] `package/server-ee/app/docker/views/services/services.html`

#### Task 2.18: Docker Host Management
- [x] `package/server-ce/app/docker/views/host/host-view.html`
- [x] `package/server-ee/app/docker/views/host/host-view.html`
- [x] `package/server-ce/app/react/docker/host/BrowseView/AgentHostBrowser.tsx`
- [x] `package/server-ee/app/react/docker/host/BrowseView/AgentHostBrowser.tsx`

#### Task 2.19: Docker Services (More Components)
- [x] `package/server-ce/app/react/docker/services/ListView/ServicesDatatable/columns/image.tsx`
- [x] `package/server-ee/app/react/docker/services/ListView/ServicesDatatable/columns/image.tsx`
- [x] `package/server-ce/app/react/docker/services/ListView/ServicesDatatable/columns/ports.tsx`
- [x] `package/server-ee/app/react/docker/services/ListView/ServicesDatatable/columns/ports.tsx`

#### Task 2.20: Docker Create Forms
- [x] `package/server-ce/app/docker/views/containers/console/attach.html`
- [x] `package/server-ee/app/docker/views/containers/console/attach.html`
- [x] `package/server-ce/app/docker/views/containers/console/exec.html`
- [x] `package/server-ee/app/docker/views/containers/console/exec.html`
- [x] `package/server-ce/app/docker/views/configs/create/createconfig.html`
- [x] `package/server-ee/app/docker/views/configs/create/createconfig.html`
- [x] `package/server-ce/app/docker/views/secrets/create/createsecret.html`
- [x] `package/server-ee/app/docker/views/secrets/create/createsecret.html`

#### Task 2.21: Docker Component Columns
- [x] `package/server-ce/app/react/docker/configs/ListView/ConfigsDatatable/columns.tsx`
- [x] `package/server-ee/app/react/docker/configs/ListView/ConfigsDatatable/columns.tsx`
- [x] `package/server-ce/app/react/docker/networks/ListView/NestedNetworksTable.tsx`
- [x] `package/server-ee/app/react/docker/networks/ListView/NestedNetworksTable.tsx`

### Task 3: Kubernetes

#### Task 3.1: Applications
- [x] `package/server-ce/app/kubernetes/views/applications/create/createApplication.html`
- [x] `package/server-ee/app/kubernetes/views/applications/create/createApplication.html`
- [x] `package/server-ce/app/react/kubernetes/applications/ListView/ApplicationsDatatable/ApplicationsDatatable.tsx`
- [x] `package/server-ee/app/react/kubernetes/applications/ListView/ApplicationsDatatable/ApplicationsDatatable.tsx`

#### Task 3.2: Namespaces
- [x] `package/server-ce/app/react/kubernetes/namespaces/ListView/NamespacesDatatable.tsx`
- [x] `package/server-ee/app/react/kubernetes/namespaces/ListView/NamespacesDatatable.tsx`

#### Task 3.3: Nodes
- [x] `package/server-ce/app/kubernetes/views/cluster/node/node.html`
- [x] `package/server-ee/app/kubernetes/views/cluster/node/node.html`
- [x] `package/server-ce/app/react/kubernetes/cluster/HomeView/NodesDatatable/NodesDatatable.tsx`
- [x] `package/server-ee/app/react/kubernetes/cluster/HomeView/NodesDatatable/NodesDatatable.tsx`

#### Task 3.4: Volumes
- [x] `package/server-ce/app/react/kubernetes/volumes/ListView/VolumesDatatable.tsx`
- [x] `package/server-ee/app/react/kubernetes/volumes/ListView/VolumesDatatable.tsx`

#### Task 3.5: Additional Kubernetes Components
- [x] `package/server-ce/app/kubernetes/views/deploy/deploy.html`
- [x] `package/server-ee/app/kubernetes/views/deploy/deploy.html`
- [x] `package/server-ce/app/kubernetes/views/summary/summary.html`
- [x] `package/server-ee/app/kubernetes/views/summary/summary.html`

### Task 4: Business Edition (EE) Features

#### Task 4.1: Registries
- [x] `package/server-ee/app/react/portainer/registries/ListView/RegistriesDatatable/RegistriesDatatable.tsx`

#### Task 4.2: RBAC (Roles & Teams)
- [x] `package/server-ee/app/portainer/rbac/views/roles/roles.html`
- [x] `package/server-ee/app/react/portainer/users/RolesView/RbacRolesDatatable.tsx`
- [x] `package/server-ee/app/react/portainer/users/teams/ListView/TeamsDatatable/TeamsDatatable.tsx`

#### Task 4.3: GitOps
- [x] `package/server-ee/app/react/portainer/gitops/GitForm.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/GitForm.stories.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/GitFormUrlField.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/TimeWindowDisplay.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/AuthFieldset/AuthFieldset.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/AutoUpdateFieldset/AutoUpdateFieldset.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/AutoUpdateFieldset/IntervalField.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/AutoUpdateFieldset/WebhookSettings.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/RefField/RefField.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/RefField/RefSelector.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/RelativePathFieldset/RelativePathFieldset.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/AdditionalFilesField.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/GitCommitLink.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/InfoPanel.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/AuthFieldset/CredentialSelector.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/AutoUpdateFieldset/AutoUpdateSettings.test.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/AutoUpdateFieldset/ForceDeploymentSwitch.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/AuthFieldset/NewCredentialForm.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/ComposePathField/ComposePathField.test.tsx`
- [x] `package/server-ee/app/react/portainer/gitops/GitFormUrlField.test.tsx`

### Task 5: Additional React Components

#### Task 5.1: Authentication Components
- [x] `package/server-ce/app/react/auth/`
- [x] `package/server-ee/app/react/auth/`

#### Task 5.2: Common Components
- [x] `package/server-ce/app/react/common/stacks/CreateView/NameField.tsx`
- [x] `package/server-ee/app/react/common/stacks/CreateView/NameField.tsx`
- [x] `package/server-ce/app/react/common/stacks/common/form-texts.tsx`
- [x] `package/server-ee/app/react/common/stacks/common/form-texts.tsx`
- [x] `package/server-ce/app/react/common/stacks/ItemView/StackContainersDatatable.tsx`
- [x] `package/server-ee/app/react/common/stacks/ItemView/StackContainersDatatable.tsx`

#### Task 5.3: Edge Components
- [x] `package/server-ce/app/react/edge/`
- [x] `package/server-ee/app/react/edge/`

#### Task 5.4: Sidebar Components
- [x] `package/server-ce/app/react/sidebar/`
- [x] `package/server-ee/app/react/sidebar/`

### Task 6: Additional Portainer Components

#### Task 6.1: Components & Directives
- [x] `package/server-ce/app/portainer/components/`
- [x] `package/server-ee/app/portainer/components/`
- [x] `package/server-ee/app/portainer/directives/` (EE Only)

#### Task 6.2: Host Management
- [x] `package/server-ce/app/portainer/hostmanagement/`
- [x] `package/server-ee/app/portainer/hostmanagement/`

#### Task 6.3: OAuth & RBAC
- [x] `package/server-ce/app/portainer/oauth/`
- [x] `package/server-ee/app/portainer/oauth/`
- [x] `package/server-ce/app/portainer/rbac/`
- [x] `package/server-ee/app/portainer/rbac/`

#### Task 6.4: Registry & License Management
- [x] `package/server-ce/app/portainer/registry-management/`
- [x] `package/server-ee/app/portainer/registry-management/`
- [x] `package/server-ce/app/portainer/license-management/`
- [x] `package/server-ee/app/portainer/license-management/`

#### Task 6.5: User Activity
- [x] `package/server-ce/app/portainer/user-activity/`
- [x] `package/server-ee/app/portainer/user-activity/`

### Task 7: Additional EE-Only Components

#### Task 7.1: Feature Flags
- [x] `package/server-ee/app/portainer/feature-flags/`

#### Task 7.2: Azure Components
- [x] `package/server-ee/app/react/azure/`

*This plan is a living document. Please update it as tasks are completed.*