
# Debugging AngularJS Parser Errors

When you see an AngularJS parser error (`[$parse:syntax]`) in the console, it's likely due to an incorrect expression in an HTML template.

**The Fix:**

1.  **Filters in Arrays:** When using a filter (like `translate`) inside an array in an attribute, wrap the expression in parentheses.
    *   **Incorrect:** `breadcrumbs="['docker.networks.title' | translate:'Networks']"`
    *   **Correct:** `breadcrumbs="[('docker.networks.title' | translate:'Networks')]"`

2.  **Interpolation in Attributes:** Don't use `{{}}` in attributes.
    *   **Incorrect:** `title="{{ 'portainer.stacks.title' | translate: 'Stacks list' }}"`
    *   **Correct:** `title="'portainer.stacks.title' | translate: 'Stacks list'"`
