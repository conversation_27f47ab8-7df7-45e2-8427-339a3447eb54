@i18n/i18n.md now we can work on Task 2.7: Additional Docker Components in this plan for our i18n support feature. Please follow all rules in this plan and work on Task 2.3: Volumes. Do not commit any change when done, and do run type, lint check after all then i18n:sync to sync the key into translation.json, then finally build the project to make sure all working good. Don't forget to mark the task and subtask as done in i18n.md. 